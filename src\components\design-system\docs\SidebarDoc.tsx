import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { ChevronLeft, Activity, Database, Zap, Settings, Search } from 'lucide-react';

const SidebarDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Sidebar"
      description="A responsive navigation sidebar component that provides access to the main sections of the application, with collapsible and expandable states."
      importCode={`import { Sidebar } from '../components/Sidebar';`}
      usage={
        <div className="w-full bg-gray-50 dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="w-full h-80 flex">
            {/* Simulated Sidebar */}
            <div className="w-64 h-full bg-white dark:bg-gray-800 rounded-l-lg border border-gray-200 dark:border-gray-700 overflow-hidden flex flex-col">
              {/* Header */}
              <div className="h-16 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 justify-between">
                <div className="h-8 w-28 bg-blue-600 dark:bg-blue-500 rounded"></div>
                <div className="h-6 w-6 rounded-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                  <ChevronLeft className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                </div>
              </div>
              
              {/* Navigation Items */}
              <div className="flex-1 overflow-y-auto p-2">
                <div className="mb-4">
                  <div className="px-3 py-2 mb-1 rounded bg-blue-50 dark:bg-blue-900/30 flex items-center">
                    <div className="h-5 w-5 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3">
                      <Activity className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="h-4 w-20 bg-blue-600/20 dark:bg-blue-500/30 rounded"></div>
                  </div>
                  
                  <div className="px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700/50 flex items-center">
                    <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mr-3">
                      <Database className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                  
                  <div className="px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700/50 flex items-center">
                    <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mr-3">
                      <Zap className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div className="h-4 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
                
                <div className="mb-1 px-3 text-xs text-gray-500 uppercase tracking-wider font-semibold">
                  Settings
                </div>
                
                <div className="px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700/50 flex items-center">
                  <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mr-3">
                    <Settings className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                </div>
              </div>
              
              {/* Footer */}
              <div className="h-16 border-t border-gray-200 dark:border-gray-700 flex items-center px-3">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600"></div>
                  <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                </div>
              </div>
            </div>
            
            {/* Simulated Content */}
            <div className="flex-1 bg-gray-100 dark:bg-gray-850 rounded-r-lg border-t border-r border-b border-gray-200 dark:border-gray-700 p-4">
              <div className="h-full rounded bg-white dark:bg-gray-800 flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
                Content Area
              </div>
            </div>
          </div>
        </div>
      }
      code={`// Typically used in the Layout component
import { Sidebar } from '../components/Sidebar';
import { useLocation } from 'react-router-dom';

function Layout({ children }) {
  const location = useLocation();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  
  return (
    <div className="flex h-screen">
      <Sidebar 
        isOpen={isSidebarOpen} 
        onToggle={() => setIsSidebarOpen(!isSidebarOpen)}
        currentRoute={location.pathname}
        onSidebarClose={() => setIsSidebarOpen(false)}
      />
      <main className="flex-1 overflow-auto">
        {children}
      </main>
    </div>
  );
}`}
      props={[
        {
          name: 'isOpen',
          type: 'boolean',
          description: 'Controls the visibility of the sidebar on mobile devices.',
          required: true
        },
        {
          name: 'onToggle',
          type: '() => void',
          description: 'Function called when the sidebar toggle button is clicked.',
          required: true
        },
        {
          name: 'currentRoute',
          type: 'string',
          description: 'Current route path to highlight the active navigation item.',
          required: true
        },
        {
          name: 'onSidebarClose',
          type: '() => void',
          description: 'Function called when the sidebar should be closed (e.g., after navigation on mobile).',
          required: true
        }
      ]}
      variants={[
        {
          title: 'Expanded Sidebar',
          description: 'The sidebar in its expanded state, showing full text labels for navigation items.',
          component: (
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium mb-4">Expanded Sidebar</h3>
              <div className="h-80 w-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden flex flex-col">
                <div className="h-16 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 justify-between">
                  <div className="h-8 w-28 bg-blue-600 dark:bg-blue-500 rounded"></div>
                  <div className="h-6 w-6 rounded-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                    <ChevronLeft className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  </div>
                </div>
                
                <div className="flex-1 overflow-y-auto p-2">
                  <div className="px-3 py-2 mb-1 rounded bg-blue-50 dark:bg-blue-900/30 flex items-center">
                    <div className="h-5 w-5 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3">
                      <Activity className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="h-4 w-20 bg-blue-600/20 dark:bg-blue-500/30 rounded"></div>
                  </div>
                  
                  <div className="px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700/50 flex items-center">
                    <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mr-3">
                      <Database className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                  
                  <div className="px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700/50 flex items-center">
                    <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mr-3">
                      <Zap className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div className="h-4 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                  
                  <div className="mt-4 mb-1 px-3 text-xs text-gray-500 uppercase tracking-wider font-semibold">
                    Settings
                  </div>
                  
                  <div className="px-3 py-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700/50 flex items-center">
                    <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mr-3">
                      <Settings className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
                
                <div className="h-16 border-t border-gray-200 dark:border-gray-700 flex items-center px-3">
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600"></div>
                    <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          ),
          code: `// The sidebar is in expanded state by default on desktop
<Sidebar 
  isOpen={isSidebarOpen} 
  onToggle={handleToggle}
  currentRoute={location.pathname}
  onSidebarClose={handleClose}
/>`
        },
        {
          title: 'Collapsed Sidebar',
          description: 'The sidebar in its collapsed state, showing only icons for navigation items to save space.',
          component: (
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium mb-4">Collapsed Sidebar</h3>
              <div className="h-80 w-16 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden flex flex-col">
                <div className="h-16 border-b border-gray-200 dark:border-gray-700 flex items-center justify-center">
                  <div className="h-8 w-8 bg-blue-600 dark:bg-blue-500 rounded"></div>
                </div>
                
                <div className="flex-1 overflow-y-auto py-2">
                  <div className="px-3 py-2 flex justify-center">
                    <div className="h-8 w-8 rounded-full bg-blue-500/20 dark:bg-blue-800/40 flex items-center justify-center">
                      <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                  
                  <div className="px-3 py-2 flex justify-center">
                    <div className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                      <Database className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    </div>
                  </div>
                  
                  <div className="px-3 py-2 flex justify-center">
                    <div className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                      <Zap className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    </div>
                  </div>
                  
                  <div className="mt-4 px-3 py-2 flex justify-center">
                    <div className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                      <Settings className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    </div>
                  </div>
                </div>
                
                <div className="h-16 border-t border-gray-200 dark:border-gray-700 flex items-center justify-center">
                  <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600"></div>
                </div>
              </div>
            </div>
          ),
          code: `// The sidebar can be collapsed by the user
// This state is managed by the SidebarContext

import { useSidebar } from '../contexts/SidebarContext';

function Layout() {
  const { isCollapsed, setIsCollapsed } = useSidebar();
  
  // The Sidebar component reads the collapsed state from context
  return (
    <div className="flex h-screen">
      <Sidebar 
        isOpen={isSidebarOpen} 
        onToggle={handleToggle}
        currentRoute={location.pathname}
        onSidebarClose={handleClose}
      />
      {/* Rest of the layout */}
    </div>
  );
}`
        },
        {
          title: 'Mobile Sidebar',
          description: 'On mobile devices, the sidebar functions as a drawer that can be opened and closed.',
          component: (
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium mb-4">Mobile Sidebar (Drawer)</h3>
              <div className="h-80 bg-gray-100 dark:bg-gray-850 rounded-lg border border-gray-200 dark:border-gray-700 relative overflow-hidden">
                {/* Mobile Header */}
                <div className="h-12 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-4">
                  <div className="h-6 w-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <svg className="h-4 w-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </div>
                  <div className="h-6 w-24 bg-blue-600 dark:bg-blue-500 rounded ml-4"></div>
                </div>
                
                {/* Content Area */}
                <div className="absolute inset-0 pt-12">
                  <div className="h-full p-4">
                    <div className="h-full rounded bg-white dark:bg-gray-800"></div>
                  </div>
                </div>
                
                {/* Sidebar Drawer - Partially Open */}
                <div className="absolute inset-y-0 left-0 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform -translate-x-1/3">
                  <div className="h-full flex flex-col">
                    <div className="h-16 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 justify-between">
                      <div className="h-8 w-28 bg-blue-600 dark:bg-blue-500 rounded"></div>
                      <div className="h-6 w-6 rounded-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                        <svg className="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </div>
                    </div>
                    
                    <div className="flex-1 p-2">
                      <div className="px-3 py-2 rounded bg-blue-50 dark:bg-blue-900/30 flex items-center">
                        <div className="h-5 w-5 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3">
                          <Activity className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div className="h-4 w-20 bg-blue-600/20 dark:bg-blue-500/30 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
              </div>
            </div>
          ),
          code: `// On mobile devices, the sidebar is controlled by the isOpen prop
// and renders as a drawer with overlay

function Layout() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  return (
    <div className="flex h-screen">
      {/* Mobile header with menu button */}
      <div className="lg:hidden h-16 fixed top-0 left-0 right-0 z-10 bg-white border-b">
        <button 
          onClick={() => setIsMobileMenuOpen(true)}
          className="p-4"
        >
          <MenuIcon />
        </button>
      </div>
      
      {/* Sidebar component */}
      <Sidebar 
        isOpen={isMobileMenuOpen} 
        onToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        currentRoute={location.pathname}
        onSidebarClose={() => setIsMobileMenuOpen(false)}
      />
      
      {/* Main content */}
      <main className="flex-1 lg:ml-64 pt-16 lg:pt-0 overflow-auto">
        {children}
      </main>
    </div>
  );
}`
        }
      ]}
      bestPractices={{
        do: [
          'Use the Sidebar component as the main navigation for authenticated users.',
          'Keep the navigation structure simple and organized by feature areas.',
          'Ensure the active state is properly highlighted to indicate current location.',
          'Allow users to collapse/expand the sidebar based on their preference.',
          'Maintain a consistent icon style throughout the navigation items.',
          'Consider grouping related navigation items into sections for better organization.'
        ],
        dont: [
          'Don\'t add too many navigation items to avoid overwhelming users.',
          'Don\'t use the sidebar for actions that should be page-specific.',
          'Don\'t place critical actions deep in nested navigation items.',
          'Don\'t override the responsive behavior without careful consideration.',
          'Don\'t use inconsistent icons or visual styles across navigation items.',
          'Don\'t assume the sidebar will always be visible - mobile users might have it collapsed.'
        ]
      }}
      accessibility={[
        'The sidebar provides a consistent navigation experience throughout the application.',
        'Active states have proper color contrast and visual indicators.',
        'Navigation items are keyboard navigable and have proper focus states.',
        'The sidebar collapse/expand functionality is accessible via keyboard.',
        'Sections and groupings provide logical organization for screen reader users.',
        'On mobile, the sidebar properly traps focus when open as a drawer.',
        'Icons have accompanying text labels when the sidebar is expanded.'
      ]}
    />
  );
};

export default SidebarDoc;