import React from 'react';
import ComponentDoc from '../ComponentDoc';

const ValidationUtilsDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Validation Utilities"
      description="A set of utility functions for validating column names, types, formulas, and their dependencies. These utilities ensure data integrity and prevent errors when working with data columns and formulas."
      
      importCode={`import { 
  validateColumnName, 
  validateColumnType, 
  validateReferencedColumns,
  validateFormulaDependencies,
  validateColumn
} from '../utils/validationUtils';`}
      
      code={`// Validate a column name
const nameValidation = await validateColumnName(
  'revenue_2023',
  existingColumns,
  { currentColumnUUID: 'col-123' }
);

// Validate a column type with a formula
const typeValidation = await validateColumnType('number', 'sum(sales) * 0.9');

// Validate that referenced columns in a formula exist
const refValidation = await validateReferencedColumns(
  'sum(sales) - sum(costs)',
  existingColumns
);

// Check for circular dependencies in formulas
const depValidation = await validateFormulaDependencies(
  'profit * 0.5',
  'bonus',
  existingColumns
);

// Comprehensive validation of a column
const validation = await validateColumn(
  'net_profit',
  'number',
  'revenue - expenses',
  existingColumns
);

if (validation.isValid) {
  console.log('Column is valid');
} else {
  console.error(validation.error);
}`}
      
      props={[
        {
          name: 'validateColumnName',
          type: '(name: string, existingColumns: UniversalColumn[], options?: ColumnValidationOptions) => Promise<ValidationResult>',
          description: 'Validates a column name against existing columns and naming rules.',
          required: false
        },
        {
          name: 'validateColumnType',
          type: '(columnType: ColumnType, formula?: string) => Promise<ValidationResult>',
          description: 'Validates that a column type is compatible with a formula (if provided).',
          required: false
        },
        {
          name: 'validateReferencedColumns',
          type: '(formula: string, existingColumns: UniversalColumn[]) => Promise<ValidationResult>',
          description: 'Validates that all columns referenced in a formula exist.',
          required: false
        },
        {
          name: 'validateFormulaDependencies',
          type: '(formula: string, columnName: string, existingColumns: UniversalColumn[], visited?: Set<string>) => Promise<ValidationResult>',
          description: 'Checks for circular dependencies in formulas.',
          required: false
        },
        {
          name: 'validateColumn',
          type: '(name: string, columnType: ColumnType, formula: string | undefined, existingColumns: UniversalColumn[], options?: ColumnValidationOptions) => Promise<ValidationResult>',
          description: 'Comprehensive validation of a column name, type, and formula.',
          required: false
        }
      ]}
      
      interfaces={[
        {
          name: 'ValidationResult',
          code: `interface ValidationResult {
  isValid: boolean;
  error?: string;
  referencedColumns: string[];
}`
        },
        {
          name: 'ColumnValidationOptions',
          code: `interface ColumnValidationOptions {
  currentColumnUUID?: string;
  allowDuplicates?: boolean;
  requireUnique?: boolean;
}`
        }
      ]}
      
      variants={[
        {
          title: 'Column Name Validation',
          description: 'Validating a column name against naming rules and existing columns.',
          component: (
            <div className="space-y-5 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Valid Column Name</h3>
                <div className="flex gap-4">
                  <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded p-3 bg-gray-50 dark:bg-gray-900 font-mono text-sm">
                    <p>name: "revenue_2023"</p>
                    <p>existingColumns: [...]</p>
                  </div>
                  <div className="flex-1 border border-green-200 dark:border-green-800 rounded p-3 bg-green-50 dark:bg-green-900/30 text-sm">
                    <p className="font-medium text-green-800 dark:text-green-200">✅ Valid</p>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">Column name follows naming rules and does not conflict with existing columns.</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Invalid Format</h3>
                <div className="flex gap-4">
                  <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded p-3 bg-gray-50 dark:bg-gray-900 font-mono text-sm">
                    <p>name: "2023-revenue"</p>
                    <p>existingColumns: [...]</p>
                  </div>
                  <div className="flex-1 border border-red-200 dark:border-red-800 rounded p-3 bg-red-50 dark:bg-red-900/30 text-sm">
                    <p className="font-medium text-red-800 dark:text-red-200">❌ Invalid</p>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">Column name must start with a letter or underscore and contain only letters, numbers, and underscores.</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Duplicate Name</h3>
                <div className="flex gap-4">
                  <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded p-3 bg-gray-50 dark:bg-gray-900 font-mono text-sm">
                    <p>name: "revenue"</p>
                    <p>existingColumns: [</p>
                    <p className="pl-2">{` "universalColumnName": "revenue" `}</p>
                    <p>]</p>
                  </div>
                  <div className="flex-1 border border-red-200 dark:border-red-800 rounded p-3 bg-red-50 dark:bg-red-900/30 text-sm">
                    <p className="font-medium text-red-800 dark:text-red-200">❌ Invalid</p>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">A column with this name already exists.</p>
                  </div>
                </div>
              </div>
            </div>
          ),
          code: `// Valid column name
const validResult = await validateColumnName('revenue_2023', existingColumns);
console.log(validResult); // { isValid: true, referencedColumns: [] }

// Invalid format (starts with a number)
const invalidFormatResult = await validateColumnName('2023-revenue', existingColumns);
console.log(invalidFormatResult); 
// { 
//   isValid: false, 
//   error: 'Column name must start with a letter or underscore and contain only letters, numbers, and underscores',
//   referencedColumns: []
// }

// Duplicate name
const duplicateResult = await validateColumnName('revenue', [{ universalColumnName: 'revenue' }]);
console.log(duplicateResult);
// {
//   isValid: false,
//   error: 'A column with this name already exists',
//   referencedColumns: []
// }`
        },
        {
          title: 'Formula Validation',
          description: 'Validating formulas for correct references and detecting circular dependencies.',
          component: (
            <div className="space-y-5 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Valid Formula</h3>
                <div className="flex gap-4">
                  <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded p-3 bg-gray-50 dark:bg-gray-900 font-mono text-sm">
                    <p>formula: "revenue - expenses"</p>
                    <p>existingColumns: [</p>
                    <p className="pl-2">{` "universalColumnName": "revenue" `},</p>
                    <p className="pl-2">{` "universalColumnName": "expenses" `}</p>
                    <p>]</p>
                  </div>
                  <div className="flex-1 border border-green-200 dark:border-green-800 rounded p-3 bg-green-50 dark:bg-green-900/30 text-sm">
                    <p className="font-medium text-green-800 dark:text-green-200">✅ Valid</p>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">All referenced columns exist and there are no circular dependencies.</p>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Referenced columns: revenue, expenses</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Missing References</h3>
                <div className="flex gap-4">
                  <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded p-3 bg-gray-50 dark:bg-gray-900 font-mono text-sm">
                    <p>formula: "revenue - costs"</p>
                    <p>existingColumns: [</p>
                    <p className="pl-2">{` "universalColumnName": "revenue" `}</p>
                    <p>]</p>
                  </div>
                  <div className="flex-1 border border-red-200 dark:border-red-800 rounded p-3 bg-red-50 dark:bg-red-900/30 text-sm">
                    <p className="font-medium text-red-800 dark:text-red-200">❌ Invalid</p>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">Referenced columns do not exist: costs</p>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Referenced columns: revenue, costs</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Circular Dependency</h3>
                <div className="flex gap-4">
                  <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded p-3 bg-gray-50 dark:bg-gray-900 font-mono text-sm overflow-auto">
                    <p>formula: "profit * 0.5"</p>
                    <p>columnName: "bonus"</p>
                    <p>existingColumns: [</p>
                    <p className="pl-2">{ `{ universalColumnName: "profit", formula: "revenue - expenses + bonus" }` }</p>
                    <p>]</p>
                  </div>
                  <div className="flex-1 border border-red-200 dark:border-red-800 rounded p-3 bg-red-50 dark:bg-red-900/30 text-sm">
                    <p className="font-medium text-red-800 dark:text-red-200">❌ Invalid</p>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">Circular dependency detected in formula</p>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Referenced columns: profit</p>
                  </div>
                </div>
              </div>
            </div>
          ),
          code: `// Valid formula with existing references
const validRefResult = await validateReferencedColumns(
  'revenue - expenses',
  [
    { universalColumnName: 'revenue' },
    { universalColumnName: 'expenses' }
  ]
);
console.log(validRefResult);
// {
//   isValid: true,
//   referencedColumns: ['revenue', 'expenses']
// }

// Missing reference
const invalidRefResult = await validateReferencedColumns(
  'revenue - costs',
  [{ universalColumnName: 'revenue' }]
);
console.log(invalidRefResult);
// {
//   isValid: false,
//   error: 'Referenced columns do not exist: costs',
//   referencedColumns: ['revenue', 'costs']
// }

// Circular dependency
const circularResult = await validateFormulaDependencies(
  'profit * 0.5',
  'bonus',
  [{ universalColumnName: 'profit', formula: 'revenue - expenses + bonus' }]
);
console.log(circularResult);
// {
//   isValid: false,
//   error: 'Circular dependency detected in formula',
//   referencedColumns: ['profit']
// }`
        }
      ]}
      
      bestPractices={{
        do: [
          'Validate column names before creating or updating columns',
          'Check for referenced columns to ensure all dependencies exist',
          'Detect circular dependencies to prevent infinite calculation loops',
          'Use the comprehensive validateColumn function for complete validation',
          'Handle validation errors gracefully with clear user feedback'
        ],
        dont: [
          'Don\'t skip validation steps when working with user-defined formulas',
          'Don\'t create columns with invalid names or types',
          'Don\'t allow circular dependencies in formulas',
          'Don\'t ignore validation errors or show technical errors to users',
          'Don\'t perform validation only on the client-side; always validate on the server as well'
        ]
      }}
      
      accessibility={[
        'Validation prevents formula errors that could lead to broken UI experiences',
        'Clear error messages help users understand and fix validation issues',
        'Preventing circular dependencies avoids calculation loops that could freeze the interface',
        'Proper column naming ensures consistent and predictable behavior throughout the application',
        'Validation provides immediate feedback to users about potential issues'
      ]}
    />
  );
};

export default ValidationUtilsDoc;