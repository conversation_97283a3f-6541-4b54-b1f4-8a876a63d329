import React, { useState, useEffect } from 'react';
import { useNotification } from '../../contexts/NotificationContext';
import { useAuth } from '../../contexts/AuthContext';
import { NotificationType, NotificationPriority } from '../../types/notificationTypes';
import axios from 'axios';
import { PhoneInput } from '../ui/PhoneInput';
import { normalizePhoneNumber } from '../../utils/phoneUtils';

// Define the subscription interface
interface SMSSubscription {
  id?: string;
  userId: string;
  phoneNumber: string;
  notificationTypes: NotificationType[];
  priorities: NotificationPriority[];
  categories?: string[];
  enabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Define the props for the component
interface SMSNotificationSubscriptionProps {
  onClose?: () => void;
}

export const SMSNotificationSubscription: React.FC<SMSNotificationSubscriptionProps> = ({ onClose }) => {
  const { user } = useAuth();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<NotificationType[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<NotificationPriority[]>([]);
  const [isEnabled, setIsEnabled] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [subscription, setSubscription] = useState<SMSSubscription | null>(null);

  // Notification types and priorities from the system
  const notificationTypes: NotificationType[] = ['success', 'error', 'info', 'warning', 'custom'];
  const notificationPriorities: NotificationPriority[] = ['low', 'medium', 'high', 'urgent'];

  // Fetch existing subscription on component mount
  useEffect(() => {
    if (user) {
      fetchSubscription();
    }
  }, [user]);

  // Fetch the user's existing subscription
  const fetchSubscription = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await axios.get('/api/notifications/sms-subscription', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success && response.data.data) {
        const sub = response.data.data;
        setSubscription(sub);
        setPhoneNumber(sub.phoneNumber || '');
        setSelectedTypes(sub.notificationTypes || []);
        setSelectedPriorities(sub.priorities || []);
        setIsEnabled(sub.enabled);
      }
    } catch (err) {
      console.error('Error fetching SMS subscription:', err);
      // It's okay if there's no subscription yet
    } finally {
      setIsLoading(false);
    }
  };

  // Save the subscription
  const saveSubscription = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate phone number
      if (!phoneNumber) {
        throw new Error('Phone number is required');
      }

      // Validate at least one type and priority
      if (selectedTypes.length === 0) {
        throw new Error('Please select at least one notification type');
      }

      if (selectedPriorities.length === 0) {
        throw new Error('Please select at least one priority level');
      }

      // Normalize phone number
      const normalizedPhone = normalizePhoneNumber(phoneNumber);

      // Prepare subscription data
      const subscriptionData: SMSSubscription = {
        userId: user?.userUUID || '',
        phoneNumber: normalizedPhone,
        notificationTypes: selectedTypes,
        priorities: selectedPriorities,
        enabled: isEnabled
      };

      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Update or create subscription
      const method = subscription ? 'put' : 'post';
      const endpoint = subscription 
        ? `/api/notifications/sms-subscription/${subscription.id}` 
        : '/api/notifications/sms-subscription';

      const response = await axios({
        method,
        url: endpoint,
        data: subscriptionData,
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success) {
        setSubscription(response.data.data);
        setSuccess('SMS notification subscription saved successfully!');
      } else {
        throw new Error(response.data.message || 'Failed to save subscription');
      }
    } catch (err: any) {
      console.error('Error saving SMS subscription:', err);
      setError(err.message || 'An error occurred while saving your subscription');
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle notification type selection
  const toggleType = (type: NotificationType) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type) 
        : [...prev, type]
    );
  };

  // Toggle priority selection
  const togglePriority = (priority: NotificationPriority) => {
    setSelectedPriorities(prev => 
      prev.includes(priority) 
        ? prev.filter(p => p !== priority) 
        : [...prev, priority]
    );
  };

  // Test the subscription by sending a test SMS
  const sendTestSMS = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (!phoneNumber) {
        throw new Error('Phone number is required');
      }

      const normalizedPhone = normalizePhoneNumber(phoneNumber);
      
      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await axios.post('/api/notifications/sms-subscription/test', {
        phoneNumber: normalizedPhone
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success) {
        setSuccess('Test SMS sent successfully!');
      } else {
        throw new Error(response.data.message || 'Failed to send test SMS');
      }
    } catch (err: any) {
      console.error('Error sending test SMS:', err);
      setError(err.message || 'An error occurred while sending the test SMS');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
      <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
        SMS Notification Subscription
      </h2>
      <p className="text-gray-600 dark:text-gray-300 mb-6">
        Subscribe to receive important notifications via SMS. Select which types of notifications you want to receive.
      </p>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <div className="space-y-6">
        {/* Phone Number Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Phone Number
          </label>
          <PhoneInput
            value={phoneNumber}
            onChange={setPhoneNumber}
            placeholder="+****************"
            className="w-full"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter your phone number in international format (e.g., +1 for US)
          </p>
        </div>

        {/* Notification Types */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notification Types
          </label>
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-5">
            {notificationTypes.map(type => (
              <div 
                key={type}
                className={`
                  px-3 py-2 rounded-md text-sm cursor-pointer border
                  ${selectedTypes.includes(type) 
                    ? 'bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900 dark:text-blue-200' 
                    : 'bg-gray-100 border-gray-300 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}
                `}
                onClick={() => toggleType(type)}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </div>
            ))}
          </div>
        </div>

        {/* Priority Levels */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Priority Levels
          </label>
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-4">
            {notificationPriorities.map(priority => (
              <div 
                key={priority}
                className={`
                  px-3 py-2 rounded-md text-sm cursor-pointer border
                  ${selectedPriorities.includes(priority) 
                    ? 'bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900 dark:text-blue-200' 
                    : 'bg-gray-100 border-gray-300 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}
                `}
                onClick={() => togglePriority(priority)}
              >
                {priority.charAt(0).toUpperCase() + priority.slice(1)}
              </div>
            ))}
          </div>
        </div>

        {/* Enable/Disable Toggle */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="enabled"
            checked={isEnabled}
            onChange={() => setIsEnabled(!isEnabled)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="enabled" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Enable SMS notifications
          </label>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <button
            type="button"
            onClick={sendTestSMS}
            disabled={isLoading || !phoneNumber}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Send Test SMS
          </button>
          <div className="space-x-2">
            {onClose && (
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
            )}
            <button
              type="button"
              onClick={saveSubscription}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Saving...' : 'Save Subscription'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
