import React, { useEffect, useState } from "react";
import { useNotification } from "../../contexts/NotificationContext";
import { formatDistanceToNow } from "date-fns";
import {
  Bell,
  Check,
  X,
  RefreshCw,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
} from "lucide-react";
import clsx from "clsx";
import { Notification } from "../../types/notificationTypes";

interface NotificationHistoryProps {
  onClose?: () => void;
  showHeader?: boolean;
}

export const NotificationHistory: React.FC<NotificationHistoryProps> = ({
  onClose,
  showHeader = true,
}) => {
  const {
    history,
    serverNotifications,
    isLoadingServerNotifications,
    fetchServerNotifications,
    markAsRead,
    removeNotification,
    clearAllNotifications,
  } = useNotification();

  const [activeTab, setActiveTab] = useState<"all" | "local" | "server">("all");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  // Helper function to convert server notification to client format
  const formatServerNotification = (serverNotification: any) => {
    // If the notification is already formatted, return it as is
    if (serverNotification.isServerNotification) {
      return serverNotification;
    }

    // Otherwise, format it
    return {
      id: serverNotification.id,
      type: serverNotification.type as any,
      title: serverNotification.title,
      content:
        typeof serverNotification.content === "string"
          ? { text: serverNotification.content }
          : JSON.parse(serverNotification.content),
      timestamp: new Date(serverNotification.created_at).getTime(),
      read: !!serverNotification.read_at,
      priority: (serverNotification.priority as any) || "medium",
      metadata: serverNotification.metadata
        ? typeof serverNotification.metadata === "string"
          ? JSON.parse(serverNotification.metadata)
          : serverNotification.metadata
        : {},
      isServerNotification: true, // Important flag to distinguish server notifications
    };
  };

  // Combine local and server notifications based on active tab
  const getVisibleNotifications = () => {
    // Filter out deleted and dismissed notifications and make sure local notifications are properly marked
    const localNotifications = history
      .filter(
        (notification) => !notification.deleted && !notification.dismissed
      ) // Filter out deleted and dismissed notifications
      .map((notification) => ({
        ...notification,
        isServerNotification:
          notification.isServerNotification === true ? true : false, // Preserve existing flag or set to false
        inHistory: true, // Mark as being in the history view to prevent auto-dismiss
      }));

    // Format server notifications if needed
    const formattedServerNotifications = serverNotifications
      .filter(
        (notification) => !notification.deleted && !notification.dismissed
      ) // Filter out deleted and dismissed notifications
      .map((notification) => {
        // Start with either the existing formatted notification or format it
        const formattedNotification = notification.isServerNotification
          ? notification
          : formatServerNotification(notification);

        // Mark as being in history view
        return {
          ...formattedNotification,
          inHistory: true, // Mark as being in the history view to prevent auto-dismiss
        };
      });

    console.log("Local notifications:", localNotifications.length);
    console.log("Server notifications:", formattedServerNotifications.length);

    // Create a map to deduplicate notifications by ID
    // This prevents showing the same notification twice if it exists in both local and server
    const notificationMap = new Map();

    // Add local notifications to the map
    localNotifications.forEach((notification) => {
      notificationMap.set(notification.id, notification);
    });

    // Add server notifications to the map, overriding local ones if they exist
    formattedServerNotifications.forEach((notification) => {
      notificationMap.set(notification.id, notification);
    });

    // Convert the map back to an array
    const deduplicated = Array.from(notificationMap.values());
    console.log("Deduplicated notifications:", deduplicated.length);

    // Determine which notifications to show based on the active tab
    let visibleNotifications;
    if (activeTab === "local") {
      visibleNotifications = localNotifications;
    } else if (activeTab === "server") {
      visibleNotifications = formattedServerNotifications;
    } else {
      // For 'all' tab, use the deduplicated list
      visibleNotifications = deduplicated;
    }

    // Sort by timestamp (newest first) and ensure we don't return null/undefined items
    return visibleNotifications
      .filter(Boolean)
      .sort((a, b) => b.timestamp - a.timestamp);
  };

  const visibleNotifications = getVisibleNotifications();
  const unreadCount = visibleNotifications.filter((n) => !n.read).length;

  // Refresh server notifications
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchServerNotifications();
    setTimeout(() => setIsRefreshing(false), 500); // Minimum spinner time for UX
  };

  // Format timestamp to relative time (e.g., "5 minutes ago")
  const formatTime = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  // Handle marking a notification as read
  const handleMarkAsRead = async (notification: any) => {
    if (notification.isServerNotification) {
      // For server notifications
      await markAsRead(notification.id);
      // The markAsRead function already calls fetchServerNotifications internally
    } else {
      // For local notifications
      await markAsRead(notification.id);
    }
  };

  // State to track which notifications are being deleted
  const [deletingNotifications, setDeletingNotifications] = useState<
    Record<string, boolean>
  >({});

  // Handle removing a notification
  const handleRemove = async (notification: any) => {
    try {
      // Set this notification as being deleted
      setDeletingNotifications((prev) => ({
        ...prev,
        [notification.id]: true,
      }));

      console.log("Removing notification from history:", notification);
      console.log("Is server notification:", notification.isServerNotification);

      if (notification.isServerNotification) {
        // For server notifications
        // Pass true for fromHistory to ensure it's marked as deleted, not removed
        await removeNotification(notification.id, true, true);
        // The removeNotification function already calls fetchServerNotifications internally
      } else {
        // For local notifications
        // Pass true for fromHistory to ensure it's marked as deleted, not removed
        await removeNotification(notification.id, false, true);
      }

      // Force a re-render by updating the state
      // This is needed because the notification might still appear in the UI
      setActiveTab(activeTab);

      // Refresh the notifications after a short delay
      setTimeout(() => {
        fetchServerNotifications();
      }, 500);
    } catch (error) {
      console.error("Error removing notification:", error);
    } finally {
      // Clear the deleting state
      setDeletingNotifications((prev) => {
        const newState = { ...prev };
        delete newState[notification.id];
        return newState;
      });
    }
  };

  // Get icon based on notification type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "error":
        return <XCircle className="w-5 h-5 text-red-500" />;
      case "warning":
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case "info":
        return <Info className="w-5 h-5 text-blue-500" />;
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden w-full max-h-[80vh] flex flex-col">
      {/* Header - only shown when showHeader is true */}
      {showHeader && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div className="flex items-center">
            <Bell className="h-5 w-5 text-blue-500 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Notifications
            </h2>
            {unreadCount > 0 && (
              <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 rounded-full">
                {unreadCount} unread
              </span>
            )}
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
      )}

      {/* Tabs */}
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        <button
          className={clsx(
            "flex-1 py-2 px-4 text-sm font-medium",
            activeTab === "all"
              ? "text-blue-600 border-b-2 border-blue-500 dark:text-blue-400 dark:border-blue-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          )}
          onClick={() => setActiveTab("all")}
        >
          All
        </button>
        <button
          className={clsx(
            "flex-1 py-2 px-4 text-sm font-medium",
            activeTab === "local"
              ? "text-blue-600 border-b-2 border-blue-500 dark:text-blue-400 dark:border-blue-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          )}
          onClick={() => setActiveTab("local")}
        >
          App
        </button>
        <button
          className={clsx(
            "flex-1 py-2 px-4 text-sm font-medium",
            activeTab === "server"
              ? "text-blue-600 border-b-2 border-blue-500 dark:text-blue-400 dark:border-blue-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          )}
          onClick={() => setActiveTab("server")}
        >
          Persistent
        </button>
      </div>

      {/* Actions */}
      <div className="p-2 bg-gray-50 dark:bg-gray-750 flex justify-between items-center">
        <button
          onClick={handleRefresh}
          disabled={isRefreshing}
          className={clsx(
            "text-xs flex items-center px-2 py-1 rounded",
            "text-gray-600 dark:text-gray-300",
            "hover:bg-gray-200 dark:hover:bg-gray-700",
            "disabled:opacity-50"
          )}
        >
          <RefreshCw
            className={clsx("h-3 w-3 mr-1", isRefreshing && "animate-spin")}
          />
          Refresh
        </button>
        <button
          onClick={async () => {
            setIsClearing(true);
            try {
              // Handle clear all based on active tab
              if (activeTab === "server") {
                // Clear all server notifications
                const serverIds = serverNotifications.map((n) => n.id);

                // Use a more resilient approach - continue even if some deletions fail
                const results = await Promise.allSettled(
                  serverIds.map((id) => removeNotification(id, true))
                );

                // Log any failures
                results.forEach((result, index) => {
                  if (result.status === "rejected") {
                    console.error(
                      `Failed to delete notification ${serverIds[index]}:`,
                      result.reason
                    );
                  }
                });

                // Refresh the list regardless of failures
                await fetchServerNotifications().catch((err) => {
                  console.error("Error refreshing notifications:", err);
                });
              } else if (activeTab === "local") {
                // Clear only local notifications
                await clearAllNotifications();
              } else {
                // Clear both local and server notifications
                await clearAllNotifications();

                // Handle server notifications
                const serverIds = serverNotifications.map((n) => n.id);

                // Use a more resilient approach - continue even if some deletions fail
                const results = await Promise.allSettled(
                  serverIds.map((id) => removeNotification(id, true))
                );

                // Log any failures
                results.forEach((result, index) => {
                  if (result.status === "rejected") {
                    console.error(
                      `Failed to delete notification ${serverIds[index]}:`,
                      result.reason
                    );
                  }
                });

                // Refresh the list regardless of failures
                await fetchServerNotifications().catch((err) => {
                  console.error("Error refreshing notifications:", err);
                });
              }

              // Force a re-render to update the UI
              setActiveTab(activeTab);
            } catch (err) {
              console.error("Error clearing notifications:", err);
            } finally {
              setIsClearing(false);
            }
          }}
          disabled={isClearing || visibleNotifications.length === 0}
          className={clsx(
            "text-xs flex items-center px-2 py-1 rounded",
            "text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300",
            "hover:bg-red-50 dark:hover:bg-red-900/20",
            "disabled:opacity-50 disabled:cursor-not-allowed"
          )}
        >
          {isClearing ? (
            <>
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              Clearing...
            </>
          ) : (
            "Clear All"
          )}
        </button>
      </div>

      {/* Notification List */}
      <div className="flex-1 overflow-y-auto">
        {isLoadingServerNotifications && activeTab !== "local" ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          </div>
        ) : visibleNotifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500 dark:text-gray-400">
            <Bell className="h-8 w-8 mb-2 opacity-30" />
            <p>No notifications</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {visibleNotifications.map((notification: any) => (
              <div
                key={notification.id}
                className={clsx(
                  "p-4 hover:bg-gray-50 dark:hover:bg-gray-750",
                  !notification.read && "bg-blue-50 dark:bg-blue-900/20"
                )}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {getTypeIcon(notification.type)}
                  </div>
                  <div className="ml-3 flex-1">
                    {notification.title && (
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {notification.title}
                      </p>
                    )}
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {notification.content.text || "Notification"}
                    </p>
                    <div className="mt-1 flex items-center justify-between">
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        {formatTime(notification.timestamp)}
                      </p>
                      <div className="flex space-x-2">
                        {!notification.read && (
                          <button
                            onClick={() => handleMarkAsRead(notification)}
                            className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
                          >
                            <Check className="h-3 w-3 mr-1" />
                            Read
                          </button>
                        )}
                        <button
                          onClick={() => handleRemove(notification)}
                          disabled={deletingNotifications[notification.id]}
                          className="text-xs text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 flex items-center disabled:opacity-50"
                        >
                          {deletingNotifications[notification.id] ? (
                            <>
                              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                              Deleting...
                            </>
                          ) : (
                            <>
                              <X className="h-3 w-3 mr-1" />
                              Delete
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
