/**
 * SMS Notification Service
 *
 * Handles checking notifications against SMS subscriptions and sending SMS messages
 */
import { pool } from "../config/database.js";
import { sendSMS } from "../utils/smsUtils.js";
import { logger } from "../utils/logger.js";

/**
 * Check if a notification matches any SMS subscriptions and send SMS if it does
 * @param {Object} notification The notification object
 */
export const processNotificationForSMS = async (notification) => {
  try {
    // Skip if notification doesn't have required fields
    if (
      !notification ||
      !notification.type ||
      !notification.priority ||
      !notification.userId
    ) {
      logger.warn("Skipping SMS for notification with missing fields", {
        notification,
      });
      return;
    }

    // Find all active subscriptions that match this notification
    const subscriptionsResult = await pool.query(
      `SELECT * FROM sms_notification_subscriptions
       WHERE enabled = TRUE
       AND user_id = $1`,
      [notification.userId]
    );

    if (subscriptionsResult.rows.length === 0) {
      logger.debug("No active SMS subscriptions found for user", {
        userId: notification.userId,
      });
      return;
    }

    // Check each subscription for a match
    for (const subscription of subscriptionsResult.rows) {
      const notificationTypes = JSON.parse(subscription.notification_types);
      const priorities = JSON.parse(subscription.priorities);
      const categories = subscription.categories
        ? JSON.parse(subscription.categories)
        : null;

      // Check if notification matches subscription criteria
      const typeMatches = notificationTypes.includes(notification.type);
      const priorityMatches = priorities.includes(notification.priority);

      // Check category if specified in both notification and subscription
      let categoryMatches = true;
      if (categories && categories.length > 0 && notification.category) {
        categoryMatches = categories.includes(notification.category);
      }

      // If all criteria match, send SMS
      if (typeMatches && priorityMatches && categoryMatches) {
        await sendSMSForNotification(notification, subscription.phone_number);
        logger.info("SMS notification sent", {
          notificationId: notification.id,
          userId: notification.userId,
          phoneNumber: subscription.phone_number,
        });
      }
    }
  } catch (error) {
    logger.error("Error processing notification for SMS", {
      error,
      notification,
    });
  }
};

/**
 * Format and send an SMS for a notification
 * @param {Object} notification The notification object
 * @param {string} phoneNumber The phone number to send to
 */
const sendSMSForNotification = async (notification, phoneNumber) => {
  try {
    // Format the notification content for SMS
    let messageBody = "";

    // Add notification title if available
    if (notification.title) {
      messageBody += `${notification.title}\n\n`;
    }

    // Add notification content
    if (typeof notification.content === "string") {
      messageBody += notification.content;
    } else if (notification.content && notification.content.text) {
      messageBody += notification.content.text;
    } else if (
      notification.content &&
      typeof notification.content === "object"
    ) {
      messageBody += JSON.stringify(notification.content);
    }

    // Truncate if too long (SMS limit is around 1600 characters)
    if (messageBody.length > 1500) {
      messageBody = messageBody.substring(0, 1497) + "...";
    }

    // Add app link if available
    if (notification.metadata && notification.metadata.link) {
      messageBody += `\n\nView details: ${notification.metadata.link}`;
    } else {
      messageBody += "\n\nView in app: https://app.resultid.ai/notifications";
    }

    // Send the SMS
    await sendSMS(phoneNumber, messageBody);

    return true;
  } catch (error) {
    logger.error("Error sending SMS for notification", {
      error,
      notification,
      phoneNumber,
    });
    return false;
  }
};
