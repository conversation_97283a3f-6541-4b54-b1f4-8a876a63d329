/**
 * URL Preview Result component
 * Renders URL metadata extraction results in a preview card
 */

import React from 'react';
import { UrlMetadataToolResult } from '../../../../services/agent/tools/urlMetadataTool';

interface UrlPreviewResultProps {
  result: UrlMetadataToolResult;
  onInteract?: (action: string, data?: any) => void;
  maxDescriptionLength?: number;
}

/**
 * Renders URL metadata in a preview card
 */
const UrlPreviewResult: React.FC<UrlPreviewResultProps> = ({
  result,
  onInteract,
  maxDescriptionLength = 150
}) => {
  // If the result wasn't successful, render a simple error state
  if (!result.success) {
    return (
      <div className="url-preview-error p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
        <p className="text-sm text-red-800 dark:text-red-300">
          Failed to load preview for URL: {result.url}
        </p>
        {result.error && (
          <p className="text-xs text-red-600 dark:text-red-400 mt-1">
            {result.error.message}
          </p>
        )}
      </div>
    );
  }

  // Format the URL for display
  const displayUrl = (() => {
    try {
      const url = new URL(result.url);
      return `${url.hostname}${url.pathname.length > 1 ? url.pathname : ''}`;
    } catch (e) {
      return result.url;
    }
  })();

  // Truncate description if needed
  const description = result.metadata?.description 
    ? result.metadata.description.length > maxDescriptionLength 
      ? `${result.metadata.description.slice(0, maxDescriptionLength)}...` 
      : result.metadata.description
    : 'No description available';

  return (
    <a 
      href={result.url} 
      target="_blank" 
      rel="noopener noreferrer"
      className="url-preview block no-underline"
      onClick={() => onInteract?.('link_click', { url: result.url })}
    >
      <div className="url-preview-card flex border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden hover:shadow-md transition-shadow duration-200">
        {result.metadata?.ogImage && (
          <div className="url-preview-image flex-shrink-0 w-24 h-24 sm:w-32 sm:h-32 bg-gray-100 dark:bg-gray-800">
            <img 
              src={result.metadata.ogImage} 
              alt={result.metadata.title || 'Website preview'} 
              className="w-full h-full object-cover"
              onError={(e) => {
                // Hide image on error
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </div>
        )}
        
        <div className="url-preview-content flex-grow p-3">
          <div className="url-preview-site text-xs text-gray-500 dark:text-gray-400 mb-1">
            {result.metadata?.siteName || displayUrl}
          </div>
          
          <h3 className="url-preview-title text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
            {result.metadata?.title || 'Untitled Page'}
          </h3>
          
          <p className="url-preview-description text-xs text-gray-600 dark:text-gray-300">
            {description}
          </p>
        </div>
      </div>
    </a>
  );
};

export default UrlPreviewResult;