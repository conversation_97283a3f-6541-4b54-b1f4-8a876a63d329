import React, { useState, useEffect } from "react";
import { useNotificationService } from "../../services/notificationService";
import { useNotification } from "../../contexts/NotificationContext";
import { NotificationCenter } from "./NotificationCenter";

export const NotificationDemo: React.FC = () => {
  const {
    success,
    error,
    info,
    warning,
    custom,
    notify,
    serverSuccess,
    serverError,
    serverInfo,
    serverWarning,
    serverCustom,
  } = useNotificationService();

  const {
    serverNotifications,
    isLoadingServerNotifications,
    fetchServerNotifications,
    unreadCount,
  } = useNotification();

  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [duration, setDuration] = useState("5000");
  const [notificationType, setNotificationType] = useState("info");
  const [saveToServer, setSaveToServer] = useState(false);
  const [category, setCategory] = useState("");

  // Fetch server notifications on mount
  useEffect(() => {
    fetchServerNotifications();
  }, []);

  const handleShowNotification = () => {
    const options = {
      title: title || undefined,
      duration: duration ? parseInt(duration) : undefined,
      category: category || undefined,
    };

    // Use the appropriate notification function based on saveToServer flag
    if (saveToServer) {
      switch (notificationType) {
        case "success":
          serverSuccess(message, options);
          break;
        case "error":
          serverError(message, options);
          break;
        case "warning":
          serverWarning(message, options);
          break;
        case "info":
          serverInfo(message, options);
          break;
        case "custom":
          serverCustom(message, options);
          break;
        default:
          serverInfo(message, options);
      }
    } else {
      switch (notificationType) {
        case "success":
          success(message, options);
          break;
        case "error":
          error(message, options);
          break;
        case "warning":
          warning(message, options);
          break;
        case "info":
          info(message, options);
          break;
        case "custom":
          custom(message, options);
          break;
        default:
          info(message, options);
      }
    }
  };

  const showRichNotification = () => {
    notify(
      "success",
      {
        component: (
          <div>
            <h3 className="font-bold">Data Processed</h3>
            <div className="mt-2">
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                <span>5 records updated</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                <span>2 new records created</span>
              </div>
            </div>
          </div>
        ),
      },
      {
        duration: 8000,
        title: "Rich Content Example",
        actions: [
          {
            label: "View Details",
            onClick: () => console.log("View details clicked"),
            variant: "primary",
          },
          {
            label: "Dismiss",
            onClick: () => console.log("Dismissed"),
            variant: "secondary",
          },
        ],
      }
    );
  };

  const showImageNotification = () => {
    notify(
      "info",
      {
        text: "New chart generated",
        image: "/server/assets/chart-templates/line-chart.png",
      },
      {
        title: "Data Visualization",
        duration: 10000,
      }
    );
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Notification System Demo</h1>
        <NotificationCenter />
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Basic Notification</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Title (optional)
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              placeholder="Notification Title"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Type</label>
            <select
              value={notificationType}
              onChange={(e) => setNotificationType(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="success">Success</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Message</label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full px-3 py-2 border rounded-md"
            placeholder="Notification message"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Duration (ms)
            </label>
            <input
              type="number"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              placeholder="Duration in milliseconds"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Category (optional)
            </label>
            <input
              type="text"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              placeholder="Notification category"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={saveToServer}
              onChange={(e) => setSaveToServer(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm font-medium">
              Save to server (persistent notification)
            </span>
          </label>
          <p className="text-xs text-gray-500 mt-1">
            When checked, the notification will be stored in the database and
            available across sessions and devices
          </p>
        </div>

        <button
          onClick={handleShowNotification}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          Show Notification
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Advanced Examples</h2>

        <div className="flex flex-wrap gap-4">
          <button
            onClick={showRichNotification}
            className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
          >
            Rich Content Notification
          </button>

          <button
            onClick={showImageNotification}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
          >
            Image Notification
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Server Notifications</h2>
          <div className="flex items-center">
            <button
              onClick={() => fetchServerNotifications()}
              className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm mr-2"
            >
              Refresh
            </button>
            <span className="text-sm text-gray-500">
              {unreadCount > 0
                ? `${unreadCount} unread`
                : "No unread notifications"}
            </span>
          </div>
        </div>

        {isLoadingServerNotifications ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : serverNotifications.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No server notifications found. Create one by checking "Save to
            server" above.
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {serverNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 ${
                  !notification.read_at ? "bg-blue-50 dark:bg-blue-900/20" : ""
                }`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {notification.type === "success" && (
                      <span className="text-green-500">✓</span>
                    )}
                    {notification.type === "error" && (
                      <span className="text-red-500">✗</span>
                    )}
                    {notification.type === "warning" && (
                      <span className="text-yellow-500">⚠</span>
                    )}
                    {notification.type === "info" && (
                      <span className="text-blue-500">ℹ</span>
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    {notification.title && (
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {notification.title}
                      </p>
                    )}
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {typeof notification.content === "string"
                        ? notification.content
                        : JSON.parse(notification.content).text ||
                          "Notification"}
                    </p>
                    <div className="mt-1 flex items-center justify-between">
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        {new Date(notification.created_at).toLocaleString()}
                      </p>
                      <div className="flex space-x-2">
                        {!notification.read_at && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="text-xs text-blue-500 hover:text-blue-700"
                          >
                            Mark as read
                          </button>
                        )}
                        <button
                          onClick={() =>
                            removeNotification(notification.id, true)
                          }
                          className="text-xs text-red-500 hover:text-red-700"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
