import { sanitizeString } from "@lib/utils";
import { Levels } from "./types";

/**
 * @name parseMenuItem
 * @description function to add/edit properties from modules data
 * @param item - item from functionalities modules data
 * @returns - object with parsed item data
 */
const parseMenuItem = (item: {
  description: string;
  [key: string]: any;
}): object => ({
  ...item,
  url: item["target"] || `/${sanitizeString(item.description)}`,
});

const getIncludeString = (item, key = "", target = "") => {
  if (!key || !target || !item || !item[key] || typeof item[key] !== "string")
    return false;

  return item[key].toLowerCase().includes(target);
};

const filterAdminItems = (items) =>
  items.filter((item) => !getIncludeString(item, "description", "administrar"));

export const getIsAdmin = (userData) =>
  userData &&
  userData.userTypeDescription &&
  !!userData.userTypeDescription.toLowerCase().match("admin");

export const structureMenuItems = (
  items = [],
  isAdmin,
  isModuleAllowed = null
) => {
  let parsedItems = [...items];

  if (!isAdmin) {
    parsedItems = filterAdminItems(items);
  }

  // Filter items based on module permissions if isModuleAllowed function is provided
  if (isModuleAllowed && typeof isModuleAllowed === "function") {
    parsedItems = parsedItems.filter((item) => {
      // Check if the item itself is allowed
      if (!isModuleAllowed(item.idModule)) {
        return false;
      }

      // If item has children, filter them recursively
      if (item.childrenModules && Array.isArray(item.childrenModules)) {
        item.childrenModules = item.childrenModules.filter((child) =>
          isModuleAllowed(child.idModule)
        );
      }

      return true;
    });
  }

  const structuredItems = parsedItems.reduce(
    (acc, act) => {
      const { extensions } = act || {};
      const {
        level: { value: level = 0 },
      } = extensions || {};

      const levelsArr = [Levels.TopMenu, Levels.BottomMenu];

      if (levelsArr.includes(level)) {
        acc[level] = [...(acc[level] || []), act];

        return acc;
      }

      return acc;
    },
    [[]]
  );

  return structuredItems;
};

/**
 * @name getMenuItems
 * @param items - recursive function to get all items with type "Menu" from modules
 * @param isModuleAllowed - function to check if a module is allowed based on IdFunctionality
 * @param levels - levels to get from tree
 * @param count - initial level to get
 * @returns array of parsed items of type "menu"
 */
export const getMenuItems = (
  items: any[],
  isModuleAllowed = null,
  levels = 2,
  count: number = 0
): any[] => {
  return items
    .filter((item) => item.type === "menu")
    .filter((item) => {
      // Check if module is allowed if isModuleAllowed function is provided
      if (isModuleAllowed && typeof isModuleAllowed === "function") {
        return isModuleAllowed(item.idModule);
      }
      return true;
    })
    .map((item) => {
      if (
        item.childrenModules &&
        item.childrenModules.length &&
        count < levels - 1
      ) {
        // Filter children modules based on permissions before recursing
        let filteredChildren = item.childrenModules;
        if (isModuleAllowed && typeof isModuleAllowed === "function") {
          filteredChildren = item.childrenModules.filter((child) =>
            isModuleAllowed(child.idModule)
          );
        }

        return parseMenuItem({
          ...item,
          childrenModules: getMenuItems(
            filteredChildren,
            isModuleAllowed,
            levels,
            count + 1
          ),
        });
      }

      return parseMenuItem(item);
    });
};

export const getProfileItems = (items: any[], isModuleAllowed = null) => {
  if (!Array.isArray(items)) return [];
  let _items = items.filter(({ extensions }) => {
    const {
      level: { value: level },
    } = extensions || {};
    return level === Levels.Profile;
  });

  // Filter items based on module permissions if isModuleAllowed function is provided
  if (isModuleAllowed && typeof isModuleAllowed === "function") {
    _items = _items.filter((item) => isModuleAllowed(item.idModule));
  }

  _items = _items.sort(({ extensions: extA }, { extensions: extB }) => {
    const { order: orderA } = extA || {};
    const { order: orderB } = extB || {};
    return orderB.value - orderA.value;
  });
  const subtitleIndex = _items.findIndex((x) => x.type === "menu-link");
  if (subtitleIndex > -1) {
    _items.splice(subtitleIndex, 0, {
      type: "subtitle-menu-link",
      description: "Accesos",
    });
    _items.splice(subtitleIndex, 0, {
      idModule: "elegir-otra-empresa",
      type: "menu-link",
      description: "Elegir otra empresa",
      target: "",
    });
  }
  const subtitleIndexUserconfig = _items.findIndex(
    (x) => x.description === "Configuracion de usuario"
  );
  if (subtitleIndexUserconfig > -1)
    _items.splice(subtitleIndexUserconfig, 0, {
      type: "subtitle-menu-link",
      description: "Cuenta",
    });
  return _items;
};
