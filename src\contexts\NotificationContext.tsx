import React, {
  createContext,
  useContext,
  useReducer,
  useEffect,
  useState,
} from "react";
import { v4 as uuidv4 } from "uuid";
import {
  Notification,
  NotificationType,
  NotificationPriority,
  NotificationContent,
  NotificationAction,
} from "../types/notificationTypes";
import { useSocket } from "./SocketContext";
import { useAuth } from "./AuthContext";
import axios from "axios";

interface NotificationState {
  notifications: Notification[];
  history: Notification[];
}

type NotificationActionType =
  | { type: "ADD_NOTIFICATION"; payload: Notification }
  | { type: "REMOVE_NOTIFICATION"; payload: string }
  | {
      type: "UPDATE_NOTIFICATION";
      payload: Partial<Notification> & { id: string };
    }
  | { type: "CLEAR_ALL_NOTIFICATIONS" }
  | { type: "MARK_AS_READ"; payload: string }
  | { type: "ADD_TO_HISTORY"; payload: Notification };

const initialState: NotificationState = {
  notifications: [],
  history: [],
};

const notificationReducer = (
  state: NotificationState,
  action: NotificationActionType
): NotificationState => {
  switch (action.type) {
    case "ADD_NOTIFICATION":
      return {
        ...state,
        notifications: [...state.notifications, action.payload],
      };
    case "REMOVE_NOTIFICATION":
      // Check if we're removing a server notification
      const isServerNotification = state.history.find(
        (n) => n.id === action.payload && n.isServerNotification
      );

      if (isServerNotification) {
        // For server notifications, only remove from local notifications
        // but keep in history with a "deleted" flag
        return {
          ...state,
          notifications: state.notifications.filter(
            (notification) => notification.id !== action.payload
          ),
          history: state.history.map((notification) =>
            notification.id === action.payload
              ? { ...notification, deleted: true }
              : notification
          ),
        };
      } else {
        // For local notifications, remove from both notifications and history
        return {
          ...state,
          notifications: state.notifications.filter(
            (notification) => notification.id !== action.payload
          ),
          history: state.history.filter(
            (notification) => notification.id !== action.payload
          ),
        };
      }
    case "UPDATE_NOTIFICATION":
      return {
        ...state,
        notifications: state.notifications.map((notification) =>
          notification.id === action.payload.id
            ? { ...notification, ...action.payload }
            : notification
        ),
      };
    case "CLEAR_ALL_NOTIFICATIONS":
      return {
        ...state,
        notifications: [],
      };
    case "MARK_AS_READ":
      return {
        ...state,
        notifications: state.notifications.map((notification) =>
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        ),
        history: state.history.map((notification) =>
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        ),
      };
    case "ADD_TO_HISTORY":
      // Check if notification already exists in history
      const existingIndex = state.history.findIndex(
        (n) => n.id === action.payload.id
      );

      if (existingIndex >= 0) {
        // If it exists, update it instead of adding a duplicate
        const updatedHistory = [...state.history];
        updatedHistory[existingIndex] = {
          ...updatedHistory[existingIndex],
          ...action.payload,
          // Preserve the isServerNotification flag if it exists
          isServerNotification:
            action.payload.isServerNotification !== undefined
              ? action.payload.isServerNotification
              : updatedHistory[existingIndex].isServerNotification,
        };

        return {
          ...state,
          history: updatedHistory,
        };
      }

      // If it doesn't exist, add it to the beginning of history
      return {
        ...state,
        history: [action.payload, ...state.history].slice(0, 100), // Limit history to 100 items
      };
    default:
      return state;
  }
};

interface NotificationContextType {
  notifications: Notification[];
  history: Notification[];
  serverNotifications: Notification[];
  isLoadingServerNotifications: boolean;
  serverNotificationsError: string | null;
  unreadCount: number;
  addNotification: (
    notification: Omit<Notification, "id" | "timestamp" | "read">,
    saveToServer?: boolean
  ) => Promise<string>;
  removeNotification: (
    id: string,
    fromServer?: boolean,
    fromHistory?: boolean
  ) => Promise<void>;
  updateNotification: (
    id: string,
    updates: Partial<Omit<Notification, "id">>
  ) => void;
  clearAllNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  fetchServerNotifications: (options?: {
    limit?: number;
    offset?: number;
    unreadOnly?: boolean;
    type?: string;
    category?: string;
  }) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState);
  const { socket, isConnected } = useSocket();
  const { user } = useAuth();

  // State for server notifications
  const [serverNotifications, setServerNotifications] = useState<
    Notification[]
  >([]);
  const [isLoadingServerNotifications, setIsLoadingServerNotifications] =
    useState(false);
  const [serverNotificationsError, setServerNotificationsError] = useState<
    string | null
  >(null);
  const [unreadCount, setUnreadCount] = useState(0);

  // Load history from localStorage on mount
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem("notification_history");
      if (savedHistory) {
        const parsedHistory = JSON.parse(savedHistory) as Notification[];
        parsedHistory.forEach((notification) => {
          dispatch({ type: "ADD_TO_HISTORY", payload: notification });
        });
      }
    } catch (error) {
      console.error("Failed to load notification history:", error);
    }
  }, []);

  // Save history to localStorage when it changes
  useEffect(() => {
    try {
      localStorage.setItem(
        "notification_history",
        JSON.stringify(state.history)
      );
    } catch (error) {
      console.error("Failed to save notification history:", error);
    }
  }, [state.history]);

  // Fetch server notifications when user changes
  useEffect(() => {
    if (user) {
      fetchServerNotifications();
    }
  }, [user?.id]); // Only depend on user ID, not the entire user object

  // Socket.IO integration for server-triggered notifications
  useEffect(() => {
    if (socket && isConnected) {
      // Listen for server-sent notifications
      socket.on(
        "notification",
        (data: {
          id: string;
          type: NotificationType;
          content: string | NotificationContent;
          title?: string;
          metadata?: any;
          priority?: NotificationPriority;
          category?: string;
          timestamp: number;
        }) => {
          // Create a notification object from the socket data
          const notification: Notification = {
            id: data.id,
            type: data.type,
            content:
              typeof data.content === "string"
                ? { text: data.content }
                : data.content,
            title: data.title,
            metadata: data.metadata,
            priority: data.priority || "medium",
            timestamp: data.timestamp || Date.now(),
            read: false,
            dismissible: true,
            duration: 5000, // Default duration
          };

          // Add to local notifications
          dispatch({ type: "ADD_NOTIFICATION", payload: notification });
          dispatch({ type: "ADD_TO_HISTORY", payload: notification });

          // We don't need to refresh server notifications here
          // The socket notification is already added to local state
        }
      );

      return () => {
        socket.off("notification");
      };
    }
  }, [socket, isConnected]);

  // Fetch notifications from the server
  const fetchServerNotifications = React.useCallback(
    async (options?: {
      limit?: number;
      offset?: number;
      unreadOnly?: boolean;
      type?: string;
      category?: string;
    }) => {
      if (!user) return;

      // Don't show loading indicator immediately to prevent UI flashing
      // Only show it if the request takes longer than 500ms
      const loadingTimer = setTimeout(() => {
        setIsLoadingServerNotifications(true);
      }, 500);

      setServerNotificationsError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        if (options?.limit) params.append("limit", options.limit.toString());
        if (options?.offset) params.append("offset", options.offset.toString());
        if (options?.unreadOnly) params.append("unreadOnly", "true");
        if (options?.type) params.append("type", options.type);
        if (options?.category) params.append("category", options.category);

        // Get token from localStorage
        const token = localStorage.getItem("authToken");

        if (!token) {
          console.error("No token found in localStorage");
          throw new Error("Authentication error: No token found");
        }

        console.log("Fetching server notifications...");
        const response = await axios.get(
          `/api/notifications?${params.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.data.success) {
          // Get the notifications from the response
          const fetchedNotifications = response.data.data.notifications || [];
          console.log("Fetched notifications:", fetchedNotifications.length);

          // If no notifications were returned, don't update the state
          // This prevents clearing the notifications when the server returns an empty array
          if (
            fetchedNotifications.length === 0 &&
            serverNotifications.length > 0
          ) {
            console.log(
              "No notifications returned from server, keeping existing notifications"
            );
            return;
          }

          // Mark each notification as a server notification
          const formattedServerNotifications = fetchedNotifications.map(
            (n: any) => ({
              ...n,
              isServerNotification: true,
              // Ensure content is properly formatted
              content:
                typeof n.content === "string" ? { text: n.content } : n.content,
            })
          );

          // Update server notifications state
          setServerNotifications(formattedServerNotifications);

          // Count unread notifications
          const unreadNotifications = formattedServerNotifications.filter(
            (n: Notification) => !n.read_at
          );
          setUnreadCount(unreadNotifications.length);
        } else {
          setServerNotificationsError("Failed to fetch notifications");
        }
      } catch (error) {
        console.error("Error fetching server notifications:", error);
        setServerNotificationsError("Error fetching notifications");
      } finally {
        clearTimeout(loadingTimer);
        setIsLoadingServerNotifications(false);
      }
    },
    [user, serverNotifications.length]
  ); // Re-create this function when user or serverNotifications.length changes

  // Add a notification (locally or to server)
  const addNotification = async (
    notification: Omit<Notification, "id" | "timestamp" | "read">,
    saveToServer = false
  ): Promise<string> => {
    const id = uuidv4();
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: Date.now(),
      read: false,
    };

    // Add to local state
    dispatch({ type: "ADD_NOTIFICATION", payload: newNotification });
    dispatch({ type: "ADD_TO_HISTORY", payload: newNotification });

    // Auto-dismiss after duration if specified, but ONLY for toast notifications
    // Do NOT auto-dismiss notifications in the history view
    if (notification.duration !== null && notification.duration !== undefined) {
      console.log(
        `Setting auto-dismiss for notification ${id} after ${notification.duration}ms`
      );

      // Only remove from active notifications, not from history
      setTimeout(() => {
        console.log(`Auto-dismissing notification ${id}`);
        // Use a special flag to indicate this is just a toast dismiss, not a full delete
        dispatch({
          type: "UPDATE_NOTIFICATION",
          payload: {
            id,
            dismissed: true,
          },
        });

        // Remove from active notifications only
        const updatedNotifications = state.notifications.filter(
          (n) => n.id !== id
        );
        dispatch({
          type: "CLEAR_ALL_NOTIFICATIONS",
        });

        // Re-add all notifications except the dismissed one
        updatedNotifications.forEach((n) => {
          dispatch({ type: "ADD_NOTIFICATION", payload: n });
        });
      }, notification.duration);
    }

    // Save to server if requested and user is authenticated
    if (saveToServer && user) {
      try {
        const response = await axios.post(
          "/api/notifications",
          {
            type: notification.type,
            content: notification.content,
            title: notification.title,
            priority: notification.priority,
            category: notification.metadata?.category,
            metadata: notification.metadata,
            expiresAt: notification.metadata?.expiresAt,
          },
          {
            headers: {
              Authorization: `Bearer ${user.token}`,
            },
          }
        );

        if (response.data.success) {
          // Refresh server notifications
          fetchServerNotifications();
          return response.data.data[0].id;
        }
      } catch (error) {
        console.error("Error saving notification to server:", error);
      }
    }

    return id;
  };

  // Remove a notification
  const removeNotification = async (
    id: string,
    fromServer = false,
    fromHistory = false
  ): Promise<void> => {
    console.log(
      `Removing notification ${id}, fromServer=${fromServer}, fromHistory=${fromHistory}`
    );

    // Check if this notification is in history
    const notificationInHistory = state.history.find((n) => n.id === id);

    if (
      fromHistory ||
      (notificationInHistory && notificationInHistory.inHistory)
    ) {
      console.log(
        `Notification ${id} is in history, marking as deleted instead of removing`
      );
      // For notifications in history, mark as deleted instead of removing
      dispatch({
        type: "UPDATE_NOTIFICATION",
        payload: {
          id,
          deleted: true,
        },
      });
    } else {
      // For regular notifications, remove from local state
      dispatch({ type: "REMOVE_NOTIFICATION", payload: id });
    }

    // Remove from server if requested and user is authenticated
    if (fromServer && user) {
      try {
        // Get token from localStorage
        const token = localStorage.getItem("authToken");

        if (!token) {
          console.error("No token found in localStorage");
          throw new Error("Authentication error: No token found");
        }

        console.log(`Making API call to delete notification ${id} from server`);
        // Make the API call to delete the notification
        await axios.delete(`/api/notifications/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        // Refresh server notifications
        console.log(`Refreshing server notifications after deleting ${id}`);
        await fetchServerNotifications();

        return Promise.resolve();
      } catch (error) {
        console.error("Error removing notification from server:", error);
        return Promise.reject(error);
      }
    }

    return Promise.resolve();
  };

  // Update a notification
  const updateNotification = (
    id: string,
    updates: Partial<Omit<Notification, "id">>
  ) => {
    dispatch({
      type: "UPDATE_NOTIFICATION",
      payload: { id, ...updates },
    });
  };

  // Clear all notifications
  const clearAllNotifications = async (): Promise<void> => {
    try {
      // Clear local notifications
      dispatch({ type: "CLEAR_ALL_NOTIFICATIONS" });

      // Also clear local notifications from history
      // Create a new action type for this specific purpose
      state.history.forEach((notification) => {
        // Only remove local notifications from history
        // Server notifications are handled separately
        if (!notification.isServerNotification) {
          dispatch({ type: "REMOVE_NOTIFICATION", payload: notification.id });
        }
      });

      return Promise.resolve();
    } catch (error) {
      console.error("Error clearing notifications:", error);
      return Promise.reject(error);
    }

    // Note: Server notifications are handled separately in the NotificationHistory component
    // based on the active tab
  };

  // Mark a notification as read
  const markAsRead = async (id: string): Promise<void> => {
    // Mark as read in local state
    dispatch({ type: "MARK_AS_READ", payload: id });

    // Mark as read on server if user is authenticated
    if (user) {
      try {
        const serverNotification = serverNotifications.find((n) => n.id === id);

        if (serverNotification && !serverNotification.read_at) {
          // Get token from localStorage
          const token = localStorage.getItem("authToken");

          if (!token) {
            console.error("No token found in localStorage");
            throw new Error("Authentication error: No token found");
          }

          await axios.put(
            `/api/notifications/${id}/read`,
            {},
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          // Refresh server notifications
          await fetchServerNotifications();
          return Promise.resolve();
        }
      } catch (error) {
        console.error("Error marking notification as read on server:", error);
        return Promise.reject(error);
      }
    }

    return Promise.resolve();
  };

  // Mark all notifications as read
  const markAllAsRead = async (): Promise<void> => {
    // Mark all as read in local state
    state.notifications.forEach((notification) => {
      dispatch({ type: "MARK_AS_READ", payload: notification.id });
    });

    // Mark all as read on server if user is authenticated
    if (user) {
      try {
        // Get token from localStorage
        const token = localStorage.getItem("authToken");

        if (!token) {
          console.error("No token found in localStorage");
          throw new Error("Authentication error: No token found");
        }

        await axios.put(
          "/api/notifications/read-all",
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        // Refresh server notifications
        await fetchServerNotifications();
        return Promise.resolve();
      } catch (error) {
        console.error(
          "Error marking all notifications as read on server:",
          error
        );
        return Promise.reject(error);
      }
    }

    return Promise.resolve();
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications: state.notifications,
        history: state.history,
        serverNotifications,
        isLoadingServerNotifications,
        serverNotificationsError,
        unreadCount,
        addNotification,
        removeNotification,
        updateNotification,
        clearAllNotifications,
        markAsRead,
        markAllAsRead,
        fetchServerNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};
