import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, Palette } from 'lucide-react';
import clsx from 'clsx';
import DesignSystemSidebar from '../../components/design-system/DesignSystemSidebar';
import DesignSystemContent from '../../components/design-system/DesignSystemContent';

export const DesignSystem: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const path = location.pathname.split('/development/design-system/')[1] || '';
  const [activeComponent, setActiveComponent] = useState<string>(path || 'introduction');

  // Update the URL when activeComponent changes
  const handleComponentChange = (component: string) => {
    setActiveComponent(component);
    navigate(`/development/design-system/${component}`);
  };

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Header area - fixed height */}
      <div className="flex-none space-y-4 p-4">
        {/* Back button */}
        <div>
          <button
            onClick={() => navigate('/development')}
            className={clsx(
              'flex items-center space-x-2',
              'text-gray-600 dark:text-gray-300',
              'hover:text-gray-900 dark:hover:text-white',
              'transition-colors'
            )}
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Developer Tools</span>
          </button>
        </div>

        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <Palette className="h-6 w-6 text-sky-500" />
            Design System
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Explore and reference all UI components and design patterns used throughout the application
          </p>
        </div>
      </div>

      {/* Main content with sidebar layout */}
      <div className="flex-1 overflow-hidden">
        <div className="flex h-full">
          <div className="w-64 min-w-64 dark:border-gray-700 overflow-y-auto rounded">
            <DesignSystemSidebar 
              activeComponent={activeComponent}
              setActiveComponent={handleComponentChange}
            />
          </div>
          <div className="flex-1 overflow-y-auto">
            <DesignSystemContent activeComponent={activeComponent} />
          </div>
        </div>
      </div>
    </div>
  );
};