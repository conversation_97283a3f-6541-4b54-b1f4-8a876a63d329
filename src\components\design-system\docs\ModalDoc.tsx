import React, { useState } from 'react';
import ComponentDoc from '../ComponentDoc';
import { ConfirmationModal } from '../../ui/ConfirmationModal';
import { Button } from '../../ui/Button';

const ModalDoc: React.FC = () => {
  const [isDefaultModalOpen, setIsDefaultModalOpen] = useState(false);
  const [isDangerModalOpen, setIsDangerModalOpen] = useState(false);
  const [isWarningModalOpen, setIsWarningModalOpen] = useState(false);
  const [isLoadingModalOpen, setIsLoadingModalOpen] = useState(false);
  const [isCustomModalOpen, setIsCustomModalOpen] = useState(false);

  return (
    <ComponentDoc
      title="Confirmation Modal"
      description="Confirmation modals provide a focused way to ask users to confirm an action or acknowledge information. They temporarily block interactions with the main view until the user makes a decision."
      importCode={`import { ConfirmationModal } from '../components/ui/ConfirmationModal';`}
      usage={
        <>
          <Button onClick={() => setIsDefaultModalOpen(true)}>Open Modal</Button>
          
          <ConfirmationModal
            isOpen={isDefaultModalOpen}
            title="Confirm Action"
            message="Are you sure you want to perform this action? This cannot be undone."
            onConfirm={() => setIsDefaultModalOpen(false)}
            onCancel={() => setIsDefaultModalOpen(false)}
          />
        </>
      }
      code={`// State to control modal visibility
const [isModalOpen, setIsModalOpen] = useState(false);

// Render button and modal
<Button onClick={() => setIsModalOpen(true)}>
  Open Modal
</Button>

<ConfirmationModal
  isOpen={isModalOpen}
  title="Confirm Action"
  message="Are you sure you want to perform this action? This cannot be undone."
  onConfirm={() => {
    // Handle confirmation action
    setIsModalOpen(false);
  }}
  onCancel={() => setIsModalOpen(false)}
/>`}
      props={[
        {
          name: 'isOpen',
          type: 'boolean',
          description: 'Controls whether the modal is displayed or hidden.',
          required: true
        },
        {
          name: 'title',
          type: 'string',
          description: 'The title displayed at the top of the modal.',
          required: true
        },
        {
          name: 'message',
          type: 'string | React.ReactNode',
          description: 'The main content of the modal. Can be a string or a React component.',
          required: true
        },
        {
          name: 'onConfirm',
          type: '() => void',
          description: 'Function called when the confirm button is clicked.',
          required: true
        },
        {
          name: 'onCancel',
          type: '() => void',
          description: 'Function called when the cancel button is clicked or when clicking outside the modal.',
          required: true
        },
        {
          name: 'confirmLabel',
          type: 'string',
          default: '"Confirm"',
          description: 'Text displayed on the confirm button.',
          required: false
        },
        {
          name: 'cancelLabel',
          type: 'string',
          default: '"Cancel"',
          description: 'Text displayed on the cancel button.',
          required: false
        },
        {
          name: 'variant',
          type: '"danger" | "warning" | "default"',
          default: '"default"',
          description: 'The visual style of the modal.',
          required: false
        },
        {
          name: 'isLoading',
          type: 'boolean',
          default: 'false',
          description: 'When true, shows a loading state on the confirm button and disables interactions.',
          required: false
        },
        {
          name: 'size',
          type: '"sm" | "md" | "lg"',
          default: '"md"',
          description: 'Controls the width of the modal.',
          required: false
        },
        {
          name: 'hideCloseButton',
          type: 'boolean',
          default: 'false',
          description: 'When true, hides the X close button in the header.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Default Modal',
          description: 'The standard modal for most confirmation scenarios.',
          component: (
            <>
              <Button onClick={() => setIsDefaultModalOpen(true)}>Open Default Modal</Button>
              
              <ConfirmationModal
                isOpen={isDefaultModalOpen}
                title="Confirm Action"
                message="Are you sure you want to perform this action? This cannot be undone."
                onConfirm={() => setIsDefaultModalOpen(false)}
                onCancel={() => setIsDefaultModalOpen(false)}
              />
            </>
          ),
          code: `<ConfirmationModal
  isOpen={isModalOpen}
  title="Confirm Action"
  message="Are you sure you want to perform this action? This cannot be undone."
  onConfirm={handleConfirm}
  onCancel={handleCancel}
/>`
        },
        {
          title: 'Danger Modal',
          description: 'Used for destructive or irreversible actions to alert users of potential consequences.',
          component: (
            <>
              <Button variant="secondary" onClick={() => setIsDangerModalOpen(true)}>Open Danger Modal</Button>
              
              <ConfirmationModal
                isOpen={isDangerModalOpen}
                title="Delete Item"
                message="This will permanently delete this item. This action cannot be undone."
                confirmLabel="Delete"
                variant="danger"
                onConfirm={() => setIsDangerModalOpen(false)}
                onCancel={() => setIsDangerModalOpen(false)}
              />
            </>
          ),
          code: `<ConfirmationModal
  isOpen={isDeleteModalOpen}
  title="Delete Item"
  message="This will permanently delete this item. This action cannot be undone."
  confirmLabel="Delete"
  variant="danger"
  onConfirm={handleDelete}
  onCancel={handleCancel}
/>`
        },
        {
          title: 'Warning Modal',
          description: 'Used to alert users about important considerations before proceeding.',
          component: (
            <>
              <Button variant="secondary" onClick={() => setIsWarningModalOpen(true)}>Open Warning Modal</Button>
              
              <ConfirmationModal
                isOpen={isWarningModalOpen}
                title="Warning"
                message="This action will log you out of all devices. Continue?"
                confirmLabel="Continue"
                variant="warning"
                onConfirm={() => setIsWarningModalOpen(false)}
                onCancel={() => setIsWarningModalOpen(false)}
              />
            </>
          ),
          code: `<ConfirmationModal
  isOpen={isWarningModalOpen}
  title="Warning"
  message="This action will log you out of all devices. Continue?"
  confirmLabel="Continue"
  variant="warning"
  onConfirm={handleContinue}
  onCancel={handleCancel}
/>`
        },
        {
          title: 'Loading State',
          description: 'Displays a loading indicator to show that the confirmation action is in progress.',
          component: (
            <>
              <Button variant="secondary" onClick={() => setIsLoadingModalOpen(true)}>Open Loading Modal</Button>
              
              <ConfirmationModal
                isOpen={isLoadingModalOpen}
                title="Publish Document"
                message="Are you sure you want to publish this document? It will be visible to all users."
                confirmLabel="Publish"
                isLoading={true}
                onConfirm={() => {}}
                onCancel={() => setIsLoadingModalOpen(false)}
              />
            </>
          ),
          code: `<ConfirmationModal
  isOpen={isPublishModalOpen}
  title="Publish Document"
  message="Are you sure you want to publish this document? It will be visible to all users."
  confirmLabel="Publish"
  isLoading={isPublishing}
  onConfirm={handlePublish}
  onCancel={handleCancel}
/>`
        },
        {
          title: 'With React Node Content',
          description: 'Modal message can contain rich React components, not just text.',
          component: (
            <>
              <Button variant="secondary" onClick={() => setIsCustomModalOpen(true)}>Open Custom Content Modal</Button>
              
              <ConfirmationModal
                isOpen={isCustomModalOpen}
                title="Terms & Conditions"
                message={
                  <div>
                    <p className="mb-2">Please read and accept our updated terms:</p>
                    <ul className="list-disc list-inside mb-2 space-y-1">
                      <li>Your data will be processed according to our privacy policy</li>
                      <li>You must be at least 18 years old to use this service</li>
                      <li>Accounts inactive for more than 12 months may be deleted</li>
                    </ul>
                    <div className="flex items-center space-x-2 mt-4">
                      <input type="checkbox" id="terms" className="rounded" />
                      <label htmlFor="terms" className="text-sm text-gray-700 dark:text-gray-300">
                        I agree to the terms and conditions
                      </label>
                    </div>
                  </div>
                }
                confirmLabel="Accept"
                onConfirm={() => setIsCustomModalOpen(false)}
                onCancel={() => setIsCustomModalOpen(false)}
                size="lg"
              />
            </>
          ),
          code: `<ConfirmationModal
  isOpen={isTermsModalOpen}
  title="Terms & Conditions"
  message={
    <div>
      <p className="mb-2">Please read and accept our updated terms:</p>
      <ul className="list-disc list-inside mb-2 space-y-1">
        <li>Your data will be processed according to our privacy policy</li>
        <li>You must be at least 18 years old to use this service</li>
        <li>Accounts inactive for more than 12 months may be deleted</li>
      </ul>
      <div className="flex items-center space-x-2 mt-4">
        <input type="checkbox" id="terms" className="rounded" />
        <label htmlFor="terms" className="text-sm text-gray-700 dark:text-gray-300">
          I agree to the terms and conditions
        </label>
      </div>
    </div>
  }
  confirmLabel="Accept"
  onConfirm={handleAcceptTerms}
  onCancel={handleDeclineTerms}
  size="lg"
/>`
        }
      ]}
      bestPractices={{
        do: [
          'Use modals for actions that require focused user attention or confirmation.',
          'Keep modal titles clear and concise to help users quickly understand the purpose.',
          'Use the appropriate variant to match the severity of the action (danger for destructive actions).',
          'Clearly label buttons to indicate what action they will perform.',
          'Include specific details about the implications of the action in the message.'
        ],
        dont: [
          'Don\'t use modals for non-essential information that doesn\'t require user action.',
          'Don\'t include multiple complex actions within a single modal.',
          'Don\'t use vague button labels like "OK" or "Yes" - be specific about the action.',
          'Don\'t overuse modals as they interrupt the user flow.',
          'Don\'t nest modals within modals.'
        ]
      }}
      accessibility={[
        'When a modal opens, focus should automatically move to the first focusable element within the modal.',
        'Modal content should be properly labeled with ARIA attributes (dialog role and aria-labelledby).',
        'Modals should trap focus within them when open - users should not be able to tab to elements behind the modal.',
        'Pressing the Escape key should close the modal.',
        'When the modal closes, focus should return to the element that opened it.',
        'Screen readers should announce the modal when it opens.'
      ]}
    />
  );
};

export default ModalDoc;