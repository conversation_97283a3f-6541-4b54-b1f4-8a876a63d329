import React, { useState } from 'react';
import ComponentDoc from '../ComponentDoc';
import { InputPromptModal } from '../../ui/InputPromptModal';
import { ModalButton } from '../../ui/ModalButton';

const InputPromptModalDoc: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalValue, setModalValue] = useState('');

  const handleConfirm = (value: string) => {
    setModalValue(value);
    setIsModalOpen(false);
  };

  return (
    <ComponentDoc
      title="Input Prompt Modal"
      description="A modal dialog that prompts the user to enter text input. Perfect for quick edits, renamings, and capturing simple text values without leaving the current context."
      importCode={`import { InputPromptModal, showPrompt } from '../components/ui/InputPromptModal';`}
      usage={
        <div>
          <ModalButton 
            variant="primary" 
            onClick={() => setIsModalOpen(true)}
          >
            Open Input Prompt
          </ModalButton>
          
          {isModalOpen && (
            <InputPromptModal
              title="Enter a value"
              message="Please provide a name for this item."
              placeholder="Enter name here"
              initialValue=""
              confirmLabel="Save"
              cancelLabel="Cancel"
              isOpen={isModalOpen}
              onConfirm={handleConfirm}
              onCancel={() => setIsModalOpen(false)}
            />
          )}
          
          {modalValue && (
            <div className="mt-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Last submitted value: <span className="font-medium">{modalValue}</span>
              </p>
            </div>
          )}
        </div>
      }
      code={`// Component usage
const [isModalOpen, setIsModalOpen] = useState(false);

const handleConfirm = (value: string) => {
  console.log('Received value:', value);
  setIsModalOpen(false);
};

<ModalButton 
  variant="primary" 
  onClick={() => setIsModalOpen(true)}
>
  Open Input Prompt
</ModalButton>

{isModalOpen && (
  <InputPromptModal
    title="Enter a value"
    message="Please provide a name for this item."
    placeholder="Enter name here"
    initialValue=""
    confirmLabel="Save"
    cancelLabel="Cancel"
    isOpen={isModalOpen}
    onConfirm={handleConfirm}
    onCancel={() => setIsModalOpen(false)}
  />
)}

// Utility function usage (promise-based)
const handleRename = async () => {
  const newName = await showPrompt(
    "Rename Item",
    "Enter a new name for this item:",
    "Current Name",
    "New name",
    "Rename",
    "Cancel"
  );
  
  if (newName) {
    console.log('New name:', newName);
    // Perform rename action
  }
};`}
      props={[
        {
          name: 'title',
          type: 'string',
          description: 'Title displayed in the modal header.',
          required: true
        },
        {
          name: 'message',
          type: 'string',
          description: 'Optional message explaining what the user should input.',
          required: false
        },
        {
          name: 'initialValue',
          type: 'string',
          default: '""',
          description: 'Initial value for the input field.',
          required: false
        },
        {
          name: 'placeholder',
          type: 'string',
          description: 'Placeholder text for the input field when empty.',
          required: false
        },
        {
          name: 'confirmLabel',
          type: 'string',
          default: '"Confirm"',
          description: 'Text for the confirm button.',
          required: false
        },
        {
          name: 'cancelLabel',
          type: 'string',
          default: '"Cancel"',
          description: 'Text for the cancel button.',
          required: false
        },
        {
          name: 'onConfirm',
          type: '(value: string) => void',
          description: 'Function called when the confirm button is clicked, receiving the input value.',
          required: true
        },
        {
          name: 'onCancel',
          type: '() => void',
          description: 'Function called when the modal is closed or the cancel button is clicked.',
          required: true
        },
        {
          name: 'isOpen',
          type: 'boolean',
          description: 'Controls whether the modal is visible.',
          required: true
        }
      ]}
      variants={[
        {
          title: 'Utility Function Usage',
          description: 'The component also provides a utility function for more convenient usage.',
          component: (
            <ModalButton
              variant="primary"
              onClick={() => {
                // In a real implementation, this would show the modal,
                // but we just display a message for documentation purposes
                alert('In a real implementation, this would call:\n\nshowPrompt("Rename Item", "Enter a new name:", "Current Name")');
              }}
            >
              Use showPrompt Utility
            </ModalButton>
          ),
          code: `// Promise-based utility function
const handleRename = async () => {
  const newName = await showPrompt(
    "Rename Item",
    "Enter a new name for this item:",
    "Current Name" // Initial value
  );
  
  if (newName) {
    // User confirmed with a value
    console.log('New name:', newName);
  } else {
    // User cancelled the prompt
    console.log('Rename cancelled');
  }
};

<button onClick={handleRename}>Rename Item</button>`
        }
      ]}
      bestPractices={{
        do: [
          'Use for simple, single text input scenarios like renaming, tagging, or quick edits.',
          'Provide clear titles and messages to explain what input is expected.',
          'Pre-fill the input with a sensible default value when possible.',
          'Use descriptive labels for the confirm and cancel buttons.',
          'Validate the input before accepting it in your onConfirm handler.',
          'Consider using the showPrompt utility function for simpler implementation.'
        ],
        dont: [
          'Don\'t use for complex forms that require multiple fields or validations.',
          'Don\'t use for critical or destructive actions where additional confirmation is needed.',
          'Don\'t set initialValue to null or undefined; use an empty string instead.',
          'Don\'t dismiss the modal automatically on input; wait for user confirmation.',
          'Don\'t use for collecting sensitive information like passwords without proper security measures.',
          'Don\'t forget to handle the case where users cancel the prompt.'
        ]
      }}
      accessibility={[
        'Input field automatically receives focus when the modal opens.',
        'The entire input is selected by default for easy replacement.',
        'Supports keyboard navigation: Enter to confirm, Escape to cancel.',
        'Modal uses semantic HTML structure for better screen reader support.',
        'Clear visual hierarchy with titles, messages, and actions.',
        'Modal traps focus within itself while open for better keyboard navigation.'
      ]}
    />
  );
};

export default InputPromptModalDoc;