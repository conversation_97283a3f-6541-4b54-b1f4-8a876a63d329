import React, { useState } from 'react';
import ComponentDoc from '../ComponentDoc';
import { TabbedNavigation, TabbedContent, TabGroup } from '../../ui/TabbedNavigation';
import { FileText, Settings, Users, Lock, BarChart, Code } from 'lucide-react';

const TabbedNavigationDoc: React.FC = () => {
  const [activeMainTab, setActiveMainTab] = useState('general');
  const [activeSubTab, setActiveSubTab] = useState('overview');

  const tabGroups: TabGroup[] = [
    {
      id: 'general',
      label: 'General',
      subtabs: [
        { id: 'overview', label: 'Overview', icon: <FileText className="h-4 w-4" /> },
        { id: 'settings', label: 'Settings', icon: <Settings className="h-4 w-4" /> },
        { id: 'users', label: 'Users', icon: <Users className="h-4 w-4" /> }
      ]
    },
    {
      id: 'advanced',
      label: 'Advanced',
      subtabs: [
        { id: 'security', label: 'Security', icon: <Lock className="h-4 w-4" /> },
        { id: 'analytics', label: 'Analytics', icon: <BarChart className="h-4 w-4" /> },
        { id: 'developer', label: 'Developer', icon: <Code className="h-4 w-4" /> }
      ]
    }
  ];

  return (
    <ComponentDoc
      title="Tabbed Navigation"
      description="A component for organizing content into multiple tabs and subtabs, allowing users to navigate between different sections of an interface."
      importCode={`import { TabbedNavigation, TabbedContent, TabGroup } from '../components/ui/TabbedNavigation';`}
      usage={
        <div className="w-full max-w-3xl">
          <TabbedNavigation
            tabGroups={tabGroups}
            activeMainTab={activeMainTab}
            onMainTabChange={setActiveMainTab}
            activeSubTab={activeSubTab}
            onSubTabChange={setActiveSubTab}
          />
          <div className="p-4 border border-gray-200 dark:border-gray-700 border-t-0 rounded-b-lg">
            <div className="text-gray-600 dark:text-gray-300">
              Content for {activeMainTab} - {activeSubTab}
            </div>
          </div>
        </div>
      }
      code={`const [activeMainTab, setActiveMainTab] = useState('general');
const [activeSubTab, setActiveSubTab] = useState('overview');

const tabGroups: TabGroup[] = [
  {
    id: 'general',
    label: 'General',
    subtabs: [
      { id: 'overview', label: 'Overview', icon: <FileText className="h-4 w-4" /> },
      { id: 'settings', label: 'Settings', icon: <Settings className="h-4 w-4" /> },
      { id: 'users', label: 'Users', icon: <Users className="h-4 w-4" /> }
    ]
  },
  {
    id: 'advanced',
    label: 'Advanced',
    subtabs: [
      { id: 'security', label: 'Security', icon: <Lock className="h-4 w-4" /> },
      { id: 'analytics', label: 'Analytics', icon: <BarChart className="h-4 w-4" /> },
      { id: 'developer', label: 'Developer', icon: <Code className="h-4 w-4" /> }
    ]
  }
];

<TabbedNavigation
  tabGroups={tabGroups}
  activeMainTab={activeMainTab}
  onMainTabChange={setActiveMainTab}
  activeSubTab={activeSubTab}
  onSubTabChange={setActiveSubTab}
/>`}
      props={[
        {
          name: 'tabGroups',
          type: 'TabGroup[]',
          description: 'Array of tab groups, each containing a set of subtabs.',
          required: true
        },
        {
          name: 'activeMainTab',
          type: 'string',
          description: 'ID of the currently active main tab.',
          required: true
        },
        {
          name: 'onMainTabChange',
          type: '(tabId: string) => void',
          description: 'Function called when a main tab is selected.',
          required: true
        },
        {
          name: 'activeSubTab',
          type: 'string',
          description: 'ID of the currently active subtab.',
          required: true
        },
        {
          name: 'onSubTabChange',
          type: '(tabId: string) => void',
          description: 'Function called when a subtab is selected.',
          required: true
        },
        {
          name: 'mainTabClassName',
          type: 'string',
          description: 'Additional CSS classes for the main tabs container.',
          required: false
        },
        {
          name: 'subTabClassName',
          type: 'string',
          description: 'Additional CSS classes for the subtabs container.',
          required: false
        },
        {
          name: 'showMainTabsLabels',
          type: 'boolean',
          default: 'true',
          description: 'Whether to show labels for main tabs.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Basic Tabbed Navigation',
          description: 'Standard implementation with main tabs and subtabs.',
          component: (
            <div className="w-full max-w-3xl">
              <TabbedNavigation
                tabGroups={tabGroups}
                activeMainTab={activeMainTab}
                onMainTabChange={setActiveMainTab}
                activeSubTab={activeSubTab}
                onSubTabChange={setActiveSubTab}
              />
              <div className="p-4 border border-gray-200 dark:border-gray-700 border-t-0 rounded-b-lg">
                <div className="text-gray-600 dark:text-gray-300">
                  Content for {activeMainTab} - {activeSubTab}
                </div>
              </div>
            </div>
          ),
          code: `<TabbedNavigation
  tabGroups={tabGroups}
  activeMainTab={activeMainTab}
  onMainTabChange={setActiveMainTab}
  activeSubTab={activeSubTab}
  onSubTabChange={setActiveSubTab}
/>`
        },
        {
          title: 'Tabbed Content',
          description: 'Using the TabbedContent component to combine navigation with content.',
          component: (
            <div className="w-full max-w-3xl">
              <TabbedContent
                tabGroups={tabGroups}
                activeMainTab={activeMainTab}
                onMainTabChange={setActiveMainTab}
                activeSubTab={activeSubTab}
                onSubTabChange={setActiveSubTab}
              >
                <div className="text-gray-600 dark:text-gray-300">
                  Content for {activeMainTab} - {activeSubTab}
                </div>
              </TabbedContent>
            </div>
          ),
          code: `<TabbedContent
  tabGroups={tabGroups}
  activeMainTab={activeMainTab}
  onMainTabChange={setActiveMainTab}
  activeSubTab={activeSubTab}
  onSubTabChange={setActiveSubTab}
>
  <div className="text-gray-600 dark:text-gray-300">
    Content for {activeMainTab} - {activeSubTab}
  </div>
</TabbedContent>`
        },
        {
          title: 'Custom Styling',
          description: 'Using custom classes to style the tabs.',
          component: (
            <div className="w-full max-w-3xl">
              <TabbedNavigation
                tabGroups={tabGroups}
                activeMainTab={activeMainTab}
                onMainTabChange={setActiveMainTab}
                activeSubTab={activeSubTab}
                onSubTabChange={setActiveSubTab}
                mainTabClassName="bg-gray-100 dark:bg-gray-800"
                subTabClassName="bg-gray-50 dark:bg-gray-750"
              />
            </div>
          ),
          code: `<TabbedNavigation
  tabGroups={tabGroups}
  activeMainTab={activeMainTab}
  onMainTabChange={setActiveMainTab}
  activeSubTab={activeSubTab}
  onSubTabChange={setActiveSubTab}
  mainTabClassName="bg-gray-100 dark:bg-gray-800"
  subTabClassName="bg-gray-50 dark:bg-gray-750"
/>`
        }
      ]}
      bestPractices={{
        do: [
          'Use clear, concise labels for tabs.',
          'Organize tabs in a logical order.',
          'Use icons in subtabs to provide visual cues.',
          'Keep tab content focused on a single concern or topic.',
          'Use consistent tab structures throughout your application.'
        ],
        dont: [
          'Don\'t use too many main tabs (aim for 2-5).',
          'Don\'t use too many subtabs under each main tab (aim for 3-7).',
          'Don\'t hide critical functionality under multiple levels of tabs.',
          'Don\'t use ambiguous labels for tabs.',
          'Don\'t change tab ordering dynamically as it confuses users.'
        ]
      }}
      accessibility={[
        'Ensure tab navigation is keyboard accessible with arrow keys.',
        'Provide sufficient color contrast for active and inactive tab states.',
        'Use ARIA roles and attributes appropriately to make the tabs accessible to screen readers.',
        'Ensure focus management is handled correctly when switching tabs.',
        'Consider users with low vision when designing the tab indicators and active states.'
      ]}
    />
  );
};

export default TabbedNavigationDoc;