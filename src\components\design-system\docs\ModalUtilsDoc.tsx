import React from 'react';
import ComponentDoc from '../ComponentDoc';

const ModalUtilsDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Modal Utilities"
      description="A set of utility functions for creating and managing modal dialogs throughout the application. Provides a consistent interface for alerts, confirmations, and prompts."
      
      importCode={`import { showAlert, showConfirm, showPrompt } from '../utils/modalUtils';`}
      
      code={`// Show a simple alert
showAlert('Success', 'Your changes have been saved.')
  .then(() => {
    // Code to run after the alert is closed
    console.log('Alert closed');
  });

// Show a confirmation dialog
showConfirm('Delete Item', 'Are you sure you want to delete this item?', 'Delete', 'Cancel', 'danger')
  .then((confirmed) => {
    if (confirmed) {
      // User clicked confirm
      deleteItem();
    } else {
      // User clicked cancel
      console.log('Deletion cancelled');
    }
  });

// Show a prompt for user input
showPrompt({
  title: 'Rename Item',
  message: 'Enter a new name for this item:',
  initialValue: 'Current Name',
  placeholder: 'Enter name...',
  confirmLabel: 'Rename',
  cancelLabel: 'Cancel',
  onConfirm: (value) => {
    // User confirmed with input value
    renameItem(value);
  },
  onCancel: () => {
    // User cancelled
    console.log('Rename cancelled');
  }
});`}
      
      props={[
        {
          name: 'showAlert',
          type: '(title: string, message: string) => Promise<void>',
          description: 'Displays an alert dialog with an OK button. Returns a promise that resolves when the alert is closed.',
          required: false
        },
        {
          name: 'showConfirm',
          type: '(title: string, message: string, confirmLabel?: string, cancelLabel?: string, variant?: "danger" | "warning" | "default") => Promise<boolean>',
          description: 'Displays a confirmation dialog with confirm and cancel buttons. Returns a promise that resolves to true if confirmed, false if canceled.',
          required: false
        },
        {
          name: 'showPrompt',
          type: '(options: { title: string, message?: string, initialValue?: string, placeholder?: string, confirmLabel?: string, cancelLabel?: string, onConfirm: (value: string) => void, onCancel: () => void }) => void',
          description: 'Displays a dialog with an input field for user text input.',
          required: false
        }
      ]}
      
      variants={[
        {
          title: 'Alert Dialog',
          description: 'A simple alert dialog with a message and an OK button.',
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 w-full max-w-md bg-white dark:bg-gray-800">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Success</h3>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">Your changes have been saved.</p>
                </div>
                <div className="flex justify-end">
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                    OK
                  </button>
                </div>
              </div>
            </div>
          ),
          code: `showAlert('Success', 'Your changes have been saved.')
  .then(() => {
    console.log('Alert closed');
  });`
        },
        {
          title: 'Confirmation Dialog',
          description: 'A dialog that asks for user confirmation with custom button labels and a danger variant.',
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 w-full max-w-md bg-white dark:bg-gray-800">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Delete Item</h3>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">Are you sure you want to delete this item? This action cannot be undone.</p>
                </div>
                <div className="flex justify-end space-x-3">
                  <button className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-md text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600">
                    Cancel
                  </button>
                  <button className="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700">
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ),
          code: `showConfirm(
  'Delete Item', 
  'Are you sure you want to delete this item? This action cannot be undone.', 
  'Delete', 
  'Cancel', 
  'danger'
).then((confirmed) => {
  if (confirmed) {
    // User confirmed deletion
    deleteItem();
  }
});`
        },
        {
          title: 'Prompt Dialog',
          description: 'A dialog with an input field for user text input.',
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 w-full max-w-md bg-white dark:bg-gray-800">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Rename Item</h3>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">Enter a new name for this item:</p>
                </div>
                <div>
                  <input
                    type="text"
                    value="Current Name"
                    placeholder="Enter name..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-md text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600">
                    Cancel
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                    Rename
                  </button>
                </div>
              </div>
            </div>
          ),
          code: `showPrompt({
  title: 'Rename Item',
  message: 'Enter a new name for this item:',
  initialValue: 'Current Name',
  placeholder: 'Enter name...',
  confirmLabel: 'Rename',
  cancelLabel: 'Cancel',
  onConfirm: (value) => {
    renameItem(value);
  },
  onCancel: () => {
    console.log('Rename cancelled');
  }
});`
        }
      ]}
      
      bestPractices={{
        do: [
          'Use these utilities instead of native browser dialogs (alert, confirm, prompt)',
          'Provide clear, concise messages that explain what\'s happening',
          'Use appropriate variants for different types of confirmations (danger for destructive actions)',
          'Include meaningful button labels that describe the action',
          'Handle both confirmation and cancellation in your promise callbacks'
        ],
        dont: [
          'Don\'t overuse alerts for non-critical information',
          'Don\'t use generic button labels like "Yes/No" when more descriptive ones would be clearer',
          'Don\'t include excessive text in modal dialogs',
          'Don\'t use danger variants for non-destructive actions',
          'Don\'t forget to handle the promise returned by these functions'
        ]
      }}
      
      accessibility={[
        'Modal dialogs are rendered with proper ARIA attributes for screen readers',
        'Focus is trapped within the modal when it\'s open',
        'Modal can be dismissed with the ESC key',
        'High contrast between text and background for readability',
        'Proper focus management returns focus to the triggering element when closed'
      ]}
    />
  );
};

export default ModalUtilsDoc;