import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { <PERSON>C<PERSON>, BarChart, PieChart, ChevronDown, Filter } from 'lucide-react';

const InsightsLayoutDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Insights Layout"
      description="A specialized layout component for data analytics and insights pages, optimized for data visualization and analysis workflows."
      importCode={`import { InsightsLayout } from '../components/layouts/InsightsLayout';`}
      usage={
        <div className="w-full bg-gray-50 dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="w-full h-96 bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 flex flex-col">
            {/* Insights Header */}
            <div className="h-16 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-6">
              <div className="flex items-center space-x-4">
                <div className="h-6 w-32 bg-gray-800 dark:bg-gray-200 rounded"></div>
                <div className="h-8 flex items-center space-x-1">
                  <div className="px-3 py-1 rounded bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm font-medium">
                    Insights
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="px-3 py-1.5 rounded border border-gray-200 dark:border-gray-700 flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  <div className="h-4 w-12 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  <ChevronDown className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                </div>
                <div className="px-3 py-1.5 rounded bg-blue-600 dark:bg-blue-500 text-white flex items-center space-x-2">
                  <div className="h-4 w-16 bg-white/20 rounded"></div>
                </div>
              </div>
            </div>
            
            {/* Content Area */}
            <div className="flex-1 p-6 grid grid-cols-12 gap-6">
              {/* Metrics Section */}
              <div className="col-span-12 grid grid-cols-3 gap-4">
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
                  <div className="flex justify-between mb-2">
                    <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    <div className="h-6 w-6 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                      <div className="h-3 w-3 border-r-2 border-t-2 border-green-500 dark:border-green-400 rotate-45 -translate-y-px"></div>
                    </div>
                  </div>
                  <div className="h-8 w-24 bg-gray-800 dark:bg-gray-200 rounded"></div>
                </div>
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
                  <div className="flex justify-between mb-2">
                    <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    <div className="h-6 w-6 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                      <div className="h-3 w-3 border-r-2 border-b-2 border-red-500 dark:border-red-400 rotate-45 translate-y-px"></div>
                    </div>
                  </div>
                  <div className="h-8 w-24 bg-gray-800 dark:bg-gray-200 rounded"></div>
                </div>
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
                  <div className="flex justify-between mb-2">
                    <div className="h-4 w-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    <div className="h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                      <div className="h-3 w-3 border-r-2 border-t-2 border-blue-500 dark:border-blue-400 rotate-45 -translate-y-px"></div>
                    </div>
                  </div>
                  <div className="h-8 w-24 bg-gray-800 dark:bg-gray-200 rounded"></div>
                </div>
              </div>
              
              {/* Charts Section */}
              <div className="col-span-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
                <div className="flex justify-between mb-4">
                  <div className="h-5 w-32 bg-gray-800 dark:bg-gray-200 rounded"></div>
                  <div className="flex space-x-2">
                    <div className="h-6 w-6 rounded bg-gray-100 dark:bg-gray-700"></div>
                    <div className="h-6 w-6 rounded bg-gray-100 dark:bg-gray-700"></div>
                  </div>
                </div>
                <div className="h-48 w-full flex items-center justify-center">
                  <LineChart className="h-32 w-32 text-gray-300 dark:text-gray-600" />
                </div>
              </div>
              
              {/* Analysis Panel */}
              <div className="col-span-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
                <div className="h-5 w-24 bg-gray-800 dark:bg-gray-200 rounded mb-4"></div>
                <div className="space-y-3">
                  <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      }
      code={`// Usage in Router configuration
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { InsightsLayout } from '../components/layouts/InsightsLayout';
import DashboardInsights from '../pages/insights/DashboardInsights';
import PerformanceAnalysis from '../pages/insights/PerformanceAnalysis';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route element={<InsightsLayout />}>
          <Route path="/insights" element={<DashboardInsights />} />
          <Route path="/insights/performance" element={<PerformanceAnalysis />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}`}
      props={[
        {
          name: 'children',
          type: 'React.ReactNode',
          description: 'Content rendered inside the layout. Generally not used directly since this component uses React Router\'s Outlet.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Dashboard Insights View',
          description: 'The insights layout configured for a dashboard-style overview with multiple data visualizations.',
          component: (
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium mb-4">Dashboard Insights</h3>
              <div className="h-80 bg-gray-50 dark:bg-gray-850 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 p-3 shadow-sm">
                    <div className="flex justify-between mb-2">
                      <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      <BarChart className="h-4 w-4 text-blue-500" />
                    </div>
                    <div className="h-6 w-20 bg-gray-800 dark:bg-gray-200 rounded"></div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 p-3 shadow-sm">
                    <div className="flex justify-between mb-2">
                      <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      <LineChart className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="h-6 w-20 bg-gray-800 dark:bg-gray-200 rounded"></div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 p-3 shadow-sm">
                    <div className="flex justify-between mb-2">
                      <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      <PieChart className="h-4 w-4 text-purple-500" />
                    </div>
                    <div className="h-6 w-20 bg-gray-800 dark:bg-gray-200 rounded"></div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-1 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 p-4 shadow-sm h-40">
                    <div className="h-5 w-24 bg-gray-800 dark:bg-gray-200 rounded mb-3"></div>
                    <div className="flex-1 flex items-center justify-center">
                      <BarChart className="h-20 w-20 text-gray-300 dark:text-gray-600" />
                    </div>
                  </div>
                  <div className="col-span-1 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 p-4 shadow-sm h-40">
                    <div className="h-5 w-24 bg-gray-800 dark:bg-gray-200 rounded mb-3"></div>
                    <div className="flex-1 flex items-center justify-center">
                      <PieChart className="h-20 w-20 text-gray-300 dark:text-gray-600" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ),
          code: `// DashboardInsights.tsx - A component that uses the InsightsLayout
import React from 'react';
import { KPICard, LineChart, BarChart, InsightPanel } from '../components';

function DashboardInsights() {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Dashboard Insights</h1>
      
      <div className="grid grid-cols-3 gap-4 mb-6">
        <KPICard 
          title="Total Revenue" 
          value="$128,450" 
          change={12.5} 
          trend="up" 
        />
        <KPICard 
          title="Average Order" 
          value="$85.20" 
          change={-2.3} 
          trend="down" 
        />
        <KPICard 
          title="Conversion Rate" 
          value="3.8%" 
          change={0.4} 
          trend="up" 
        />
      </div>
      
      <div className="grid grid-cols-2 gap-6">
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium mb-4">Revenue Trends</h2>
          <LineChart data={revenueData} />
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium mb-4">Customer Demographics</h2>
          <BarChart data={customerData} />
        </div>
      </div>
    </div>
  );
}

export default DashboardInsights;`
        },
        {
          title: 'Data Analysis View',
          description: 'The insights layout configured for detailed data analysis with filters and data tables.',
          component: (
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium mb-4">Data Analysis View</h3>
              <div className="h-80 bg-gray-50 dark:bg-gray-850 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                <div className="flex justify-between mb-4">
                  <div className="h-6 w-32 bg-gray-800 dark:bg-gray-200 rounded"></div>
                  <div className="flex space-x-2">
                    <div className="px-3 py-1 border border-gray-200 dark:border-gray-700 rounded flex items-center space-x-2">
                      <Filter className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                      <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    </div>
                    <div className="px-3 py-1 border border-gray-200 dark:border-gray-700 rounded">
                      <div className="h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
                  <div className="border-b border-gray-200 dark:border-gray-700 px-4 py-3 bg-gray-50 dark:bg-gray-800">
                    <div className="grid grid-cols-4 gap-4">
                      <div className="h-4 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      <div className="h-4 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      <div className="h-4 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      <div className="h-4 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    </div>
                  </div>
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {[1, 2, 3, 4].map(i => (
                      <div key={i} className="px-4 py-3">
                        <div className="grid grid-cols-4 gap-4">
                          <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ),
          code: `// DataAnalysisView.tsx - A component that uses the InsightsLayout
import React, { useState } from 'react';
import { DataTable, FilterPanel, ExportButton } from '../components';

function DataAnalysisView() {
  const [filters, setFilters] = useState({
    dateRange: 'last30Days',
    segment: 'all'
  });
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Customer Data Analysis</h1>
        <div className="flex space-x-3">
          <FilterPanel 
            filters={filters} 
            onChange={setFilters}
          />
          <ExportButton />
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <DataTable 
          columns={[
            { key: 'name', label: 'Customer' },
            { key: 'revenue', label: 'Revenue' },
            { key: 'orders', label: 'Orders' },
            { key: 'lastOrder', label: 'Last Order' }
          ]}
          data={customerData}
          filters={filters}
          pagination={{
            page: 0,
            pageSize: 10,
            total: customerData.length
          }}
        />
      </div>
    </div>
  );
}

export default DataAnalysisView;`
        }
      ]}
      bestPractices={{
        do: [
          'Use for all data analytics and insights-related pages to maintain a consistent user experience.',
          'Implement responsive design patterns suitable for data visualization and analysis.',
          'Leverage the layout\'s structure for consistent placement of filters, controls, and content areas.',
          'Ensure charts and data visualizations are accessible and include text alternatives.',
          'Organize related metrics and visualizations into logical groups or sections.',
          'Consider performance implications when displaying large datasets or complex visualizations.'
        ],
        dont: [
          'Don\'t use for general application pages - use the standard Layout component instead.',
          'Don\'t overcrowd the interface with too many visualizations or metrics at once.',
          'Don\'t neglect to provide context and explanations for complex data visualizations.',
          'Don\'t assume all users understand data visualization conventions - provide clear labels and legends.',
          'Don\'t skimp on filtering and data exploration tools that help users find insights.',
          'Don\'t create visualizations that are difficult to interpret on smaller screens.'
        ]
      }}
      accessibility={[
        'Ensure all data visualizations have appropriate text alternatives or descriptions.',
        'Maintain sufficient color contrast for all text and data visualization elements.',
        'Provide keyboard navigation for all interactive elements including charts and filters.',
        'Use semantic HTML structure for data tables and lists.',
        'Include proper ARIA attributes for custom interactive components.',
        'Consider screen reader announcements for dynamic data updates.',
        'Ensure filter controls are properly labeled and have appropriate focus states.'
      ]}
    />
  );
};

export default InsightsLayoutDoc;