import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { ShineBorder } from '../../magicui/shine-border';

const ShineBorderDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Shine Border"
      description="An animated border effect component that creates a shimmering, gradient border around elements. Perfect for highlighting important UI elements, cards, or interactive components."
      importCode={`import { ShineBorder } from '../components/magicui/shine-border';`}
      usage={
        <div className="w-64 h-40 relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4 flex items-center justify-center">
          <ShineBorder borderWidth={2} shineColor="#3b82f6" borderRadius={12} />
          <span className="text-gray-700 dark:text-gray-300">Element with Shine Border</span>
        </div>
      }
      code={`<div className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4">
  <ShineBorder borderWidth={2} shineColor="#3b82f6" borderRadius={12} />
  <span>Element with Shine Border</span>
</div>`}
      props={[
        {
          name: 'borderWidth',
          type: 'number',
          default: '1',
          description: 'Width of the border in pixels.',
          required: false
        },
        {
          name: 'duration',
          type: 'number',
          default: '14',
          description: 'Duration of the animation in seconds.',
          required: false
        },
        {
          name: 'shineColor',
          type: 'string | string[]',
          default: '"#000000"',
          description: 'Color of the border, can be a single color or an array of colors for gradient effect.',
          required: false
        },
        {
          name: 'borderRadius',
          type: 'number',
          default: '0',
          description: 'Border radius in pixels. Set to match the parent container\'s border radius.',
          required: false
        },
        {
          name: 'className',
          type: 'string',
          description: 'Additional CSS classes to apply to the border element.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Default Shine Border',
          description: 'Basic implementation with default settings.',
          component: (
            <div className="w-64 h-32 relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4 flex items-center justify-center">
              <ShineBorder />
              <span className="text-gray-700 dark:text-gray-300">Default Shine Border</span>
            </div>
          ),
          code: `<div className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4">
  <ShineBorder />
  <span>Default Shine Border</span>
</div>`
        },
        {
          title: 'Wider Border',
          description: 'A shine border with increased width for more prominence.',
          component: (
            <div className="w-64 h-32 relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4 flex items-center justify-center">
              <ShineBorder borderWidth={3} />
              <span className="text-gray-700 dark:text-gray-300">Wider Shine Border</span>
            </div>
          ),
          code: `<div className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4">
  <ShineBorder borderWidth={3} />
  <span>Wider Shine Border</span>
</div>`
        },
        {
          title: 'Custom Color',
          description: 'Using a custom color for the shine effect.',
          component: (
            <div className="w-64 h-32 relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4 flex items-center justify-center">
              <ShineBorder shineColor="#FF5733" />
              <span className="text-gray-700 dark:text-gray-300">Custom Color Border</span>
            </div>
          ),
          code: `<div className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4">
  <ShineBorder shineColor="#FF5733" />
  <span>Custom Color Border</span>
</div>`
        },
        {
          title: 'Gradient Colors',
          description: 'Multiple colors creating a gradient shine effect.',
          component: (
            <div className="w-64 h-32 relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4 flex items-center justify-center">
              <ShineBorder shineColor={["#3b82f6", "#8b5cf6", "#ec4899"]} />
              <span className="text-gray-700 dark:text-gray-300">Gradient Shine Border</span>
            </div>
          ),
          code: `<div className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4">
  <ShineBorder shineColor={["#3b82f6", "#8b5cf6", "#ec4899"]} />
  <span>Gradient Shine Border</span>
</div>`
        },
        {
          title: 'Faster Animation',
          description: 'Reducing the duration to create a faster animation cycle.',
          component: (
            <div className="w-64 h-32 relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4 flex items-center justify-center">
              <ShineBorder duration={6} shineColor="#3b82f6" />
              <span className="text-gray-700 dark:text-gray-300">Fast Shine Border</span>
            </div>
          ),
          code: `<div className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-4">
  <ShineBorder duration={6} shineColor="#3b82f6" />
  <span>Fast Shine Border</span>
</div>`
        },
        {
          title: 'Rounded Container',
          description: 'Adding border radius to match a rounded container.',
          component: (
            <div className="w-64 h-32 relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4 flex items-center justify-center">
              <ShineBorder borderRadius={12} shineColor="#3b82f6" />
              <span className="text-gray-700 dark:text-gray-300">Rounded Shine Border</span>
            </div>
          ),
          code: `<div className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4">
  <ShineBorder borderRadius={12} shineColor="#3b82f6" />
  <span>Rounded Shine Border</span>
</div>`
        }
      ]}
      bestPractices={{
        do: [
          'Make sure the parent element has position: relative for proper positioning.',
          'Match the borderRadius value to the parent element\'s border radius.',
          'Use colors that complement your application\'s theme.',
          'Apply to important UI elements to draw attention to them.',
          'Use for interactive elements like cards, buttons or important containers.',
          'Consider using motion-reduced media queries to respect user preferences.'
        ],
        dont: [
          'Don\'t apply to too many elements at once, which can be visually distracting.',
          'Don\'t use highly saturated colors that might strain the user\'s eyes.',
          'Don\'t set the animation duration too fast, which can be distracting.',
          'Don\'t use on elements where the border might interfere with readability.',
          'Don\'t forget to add explicit border-radius to the parent container if using borderRadius.',
          'Don\'t use on elements without enough contrast with the background.'
        ]
      }}
      accessibility={[
        'The component respects the user\'s reduced motion preferences via the motion-safe utility class.',
        'Ensure there is adequate contrast between the shine border and the background.',
        'The animation is subtle enough not to cause distractions or motion sickness.',
        'Use the component as an enhancement, not as a primary indicator for important information.',
        'Consider disabling the animation for users with vestibular disorders.'
      ]}
    />
  );
};

export default ShineBorderDoc;