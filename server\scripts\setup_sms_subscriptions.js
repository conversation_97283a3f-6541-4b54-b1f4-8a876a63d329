/**
 * <PERSON><PERSON><PERSON> to set up the SMS notification subscriptions table
 */
import { pool } from "../config/database.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const sqlFilePath = path.join(
  __dirname,
  "../db/migrations/create_sms_subscriptions_table.sql"
);

async function setupSMSSubscriptionsTable() {
  console.log("Setting up SMS notification subscriptions table...");

  try {
    // Read the SQL file
    const sql = fs.readFileSync(sqlFilePath, "utf8");

    // Execute the SQL
    await pool.query(sql);

    console.log("SMS notification subscriptions table created successfully!");
  } catch (error) {
    console.error(
      "Error setting up SMS notification subscriptions table:",
      error
    );
  }

  // Don't close the pool here as it's already handled in the database.js file
  // via the process.on('exit') handler
}

// Run the setup
setupSMSSubscriptionsTable();
