import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { ArrowRight } from 'lucide-react';

const SuggestedPromptsDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Suggested Prompts"
      description="A component that displays a grid of suggested questions or prompts for the AI Assistant. These prompts help users get started with common queries and provide guidance on how to interact with the assistant."
      
      importCode={`import { SuggestedPrompts } from '../ui/AIAssistant/SuggestedPrompts';`}
      
      code={`<SuggestedPrompts
  suggestedPrompts={[
    "How do I analyze customer themes?",
    "What metrics should I track?",
    "Show me campaign performance",
    "Explain sentiment analysis"
  ]}
  setInputValue={setInputValue}
/>`}
      
      props={[
        {
          name: 'suggestedPrompts',
          type: 'string[]',
          description: 'Array of suggested prompt strings to display as clickable buttons.',
          required: true
        },
        {
          name: 'setInputValue',
          type: '(value: string) => void',
          description: 'Function to set the input value when a prompt is clicked.',
          required: true
        }
      ]}
      
      variants={[
        {
          title: 'Default Grid Layout',
          description: 'The default two-column grid layout for suggested prompts.',
          code: `<SuggestedPrompts
  suggestedPrompts={[
    "How do I analyze customer themes?",
    "What metrics should I track?",
    "Show me campaign performance",
    "Explain sentiment analysis"
  ]}
  setInputValue={(value) => console.log(value)}
/>`,
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden w-full max-w-md">
              <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-white dark:bg-gray-800">
                <div className="text-sm font-medium text-gray-900 dark:text-white mb-3">Suggested Questions</div>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    "How do I analyze customer themes?",
                    "What metrics should I track?",
                    "Show me campaign performance",
                    "Explain sentiment analysis"
                  ].map((prompt, index) => (
                    <button
                      key={index}
                      className="text-sm text-left p-2 rounded border border-gray-200 dark:border-gray-700 hover:bg-gray-50 
                        dark:hover:bg-gray-700 transition-colors flex items-center justify-between group"
                    >
                      <span className="text-gray-500 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white">
                        {prompt}
                      </span>
                      <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 text-blue-500 transition-opacity" />
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )
        },
        {
          title: 'Domain-Specific Prompts',
          description: 'Suggested prompts tailored to specific domains or features.',
          code: `<SuggestedPrompts
  suggestedPrompts={[
    "Analyze survey responses from Q1",
    "Compare theme sentiment across segments",
    "Show top themes by importance",
    "Generate summary report for stakeholders"
  ]}
  setInputValue={(value) => console.log(value)}
/>`,
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden w-full max-w-md">
              <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-white dark:bg-gray-800">
                <div className="text-sm font-medium text-gray-900 dark:text-white mb-3">Suggested Questions</div>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    "Analyze survey responses from Q1",
                    "Compare theme sentiment across segments",
                    "Show top themes by importance",
                    "Generate summary report for stakeholders"
                  ].map((prompt, index) => (
                    <button
                      key={index}
                      className="text-sm text-left p-2 rounded border border-gray-200 dark:border-gray-700 hover:bg-gray-50 
                        dark:hover:bg-gray-700 transition-colors flex items-center justify-between group"
                    >
                      <span className="text-gray-500 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white">
                        {prompt}
                      </span>
                      <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 text-blue-500 transition-opacity" />
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )
        },
        {
          title: 'Few Prompts',
          description: 'Display with only a couple of suggested prompts.',
          code: `<SuggestedPrompts
  suggestedPrompts={[
    "How do I get started?",
    "What's new in this version?"
  ]}
  setInputValue={(value) => console.log(value)}
/>`,
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden w-full max-w-md">
              <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-white dark:bg-gray-800">
                <div className="text-sm font-medium text-gray-900 dark:text-white mb-3">Suggested Questions</div>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    "How do I get started?",
                    "What's new in this version?"
                  ].map((prompt, index) => (
                    <button
                      key={index}
                      className="text-sm text-left p-2 rounded border border-gray-200 dark:border-gray-700 hover:bg-gray-50 
                        dark:hover:bg-gray-700 transition-colors flex items-center justify-between group"
                    >
                      <span className="text-gray-500 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white">
                        {prompt}
                      </span>
                      <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 text-blue-500 transition-opacity" />
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )
        }
      ]}
      
      bestPractices={{
        do: [
          'Keep suggested prompts concise and clear (ideally under 50 characters)',
          'Group related prompts together when using many suggestions',
          'Tailor prompts to the current context or user task',
          'Use natural language phrasing that users would actually type',
          'Update suggested prompts based on user behavior or context changes',
          'Include a diverse range of prompt types (questions, commands, etc.)'
        ],
        dont: [
          'Don\'t use overly technical or jargon-heavy language',
          'Don\'t include prompts that are too similar to each other',
          'Don\'t write prompts that are too long to display properly',
          'Don\'t use prompts that might lead to unhelpful AI responses',
          'Don\'t keep static prompts that never update based on context',
          'Don\'t include prompts that could be confusing or ambiguous'
        ]
      }}
      
      accessibility={[
        'Ensure buttons have sufficient spacing for touch targets',
        'Maintain adequate contrast between text and background colors',
        'Provide clear hover/focus states for interactive elements',
        'Ensure the component can be navigated using keyboard',
        'Keep text at a readable size for all users'
      ]}
    />
  );
};

export default SuggestedPromptsDoc;