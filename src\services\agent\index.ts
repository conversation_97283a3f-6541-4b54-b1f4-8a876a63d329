/**
 * Agent System Entry Point
 * Initializes the agent system and registers all available tools
 */

import agent<PERSON><PERSON>roller, { AgentController } from './agentController';
import toolRegistry from './toolRegistry';

// Import available tools
import urlMetadataTool from './tools/urlMetadataTool';

// Export tool categories and interfaces
export * from './tools/baseTool';

/**
 * Initialize the agent system
 * Registers all available tools with the registry
 */
export function initializeAgentSystem(): void {
  // Register all available tools
  try {
    // Integration tools
    toolRegistry.registerTool(urlMetadataTool);
    
    // Future tools to be registered as they're implemented:
    // Data navigator tools
    // Data analysis tools
    // Platform navigation tools
    // Contextual tools
    
    console.log('Agent system initialized with tools:', 
      toolRegistry.getAllTools().map(tool => tool.name).join(', '));
  } catch (error) {
    console.error('Error initializing agent system:', error);
  }
}

// Initialize the agent system when this module is imported
initializeAgentSystem();

// Export the agent controller and tool registry for direct access
export { agent<PERSON>ontroller, toolRegistry };

// Default export the agent controller
export default agentController;