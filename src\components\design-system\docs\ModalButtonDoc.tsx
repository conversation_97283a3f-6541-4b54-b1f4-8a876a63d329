import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { ModalButton } from '../../ui/ModalButton';
import { ArrowRight, Download, Loader2, Save } from 'lucide-react';

const ModalButtonDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Modal Button"
      description="A versatile button component designed specifically for use in modals, with support for multiple variants, sizes, loading states, and icon placement."
      importCode={`import { ModalButton } from '../components/ui/ModalButton';`}
      usage={
        <div className="flex space-x-2">
          <ModalButton variant="primary">Save Changes</ModalButton>
          <ModalButton variant="secondary">Cancel</ModalButton>
        </div>
      }
      code={`<ModalButton variant="primary">Save Changes</ModalButton>
<ModalButton variant="secondary">Cancel</ModalButton>`}
      props={[
        {
          name: 'variant',
          type: "'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'outline' | 'ghost'",
          default: 'primary',
          description: 'Determines the visual style of the button.',
          required: false
        },
        {
          name: 'size',
          type: "'sm' | 'md' | 'lg'",
          default: 'md',
          description: 'Controls the size of the button.',
          required: false
        },
        {
          name: 'isLoading',
          type: 'boolean',
          default: 'false',
          description: 'When true, displays a loading spinner and disables the button.',
          required: false
        },
        {
          name: 'loadingText',
          type: 'string',
          description: 'Text to display while in loading state. Defaults to the button children.',
          required: false
        },
        {
          name: 'leftIcon',
          type: 'React.ReactNode',
          description: 'Icon to display to the left of the button text.',
          required: false
        },
        {
          name: 'rightIcon',
          type: 'React.ReactNode',
          description: 'Icon to display to the right of the button text.',
          required: false
        },
        {
          name: 'fullWidth',
          type: 'boolean',
          default: 'false',
          description: 'When true, the button takes up the full width of its container.',
          required: false
        },
        {
          name: 'className',
          type: 'string',
          description: 'Additional CSS classes to apply to the button.',
          required: false
        },
        {
          name: 'disabled',
          type: 'boolean',
          default: 'false',
          description: 'When true, the button is disabled and non-interactive.',
          required: false
        },
        {
          name: 'type',
          type: "'button' | 'submit' | 'reset'",
          default: 'button',
          description: 'The HTML button type attribute.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Variant Styles',
          description: 'The button comes in several visual styles for different contexts.',
          component: (
            <div className="flex flex-wrap gap-2">
              <ModalButton variant="primary">Primary</ModalButton>
              <ModalButton variant="secondary">Secondary</ModalButton>
              <ModalButton variant="danger">Danger</ModalButton>
              <ModalButton variant="success">Success</ModalButton>
              <ModalButton variant="warning">Warning</ModalButton>
              <ModalButton variant="outline">Outline</ModalButton>
              <ModalButton variant="ghost">Ghost</ModalButton>
            </div>
          ),
          code: `<ModalButton variant="primary">Primary</ModalButton>
<ModalButton variant="secondary">Secondary</ModalButton>
<ModalButton variant="danger">Danger</ModalButton>
<ModalButton variant="success">Success</ModalButton>
<ModalButton variant="warning">Warning</ModalButton>
<ModalButton variant="outline">Outline</ModalButton>
<ModalButton variant="ghost">Ghost</ModalButton>`
        },
        {
          title: 'Button Sizes',
          description: 'Buttons are available in three sizes.',
          component: (
            <div className="flex items-center gap-2">
              <ModalButton variant="primary" size="sm">Small</ModalButton>
              <ModalButton variant="primary" size="md">Medium</ModalButton>
              <ModalButton variant="primary" size="lg">Large</ModalButton>
            </div>
          ),
          code: `<ModalButton variant="primary" size="sm">Small</ModalButton>
<ModalButton variant="primary" size="md">Medium</ModalButton>
<ModalButton variant="primary" size="lg">Large</ModalButton>`
        },
        {
          title: 'Loading State',
          description: 'Buttons can show a loading spinner to indicate processing.',
          component: (
            <div className="flex gap-2">
              <ModalButton variant="primary" isLoading>Loading...</ModalButton>
              <ModalButton variant="success" isLoading loadingText="Saving...">Save</ModalButton>
            </div>
          ),
          code: `<ModalButton variant="primary" isLoading>Loading...</ModalButton>
<ModalButton variant="success" isLoading loadingText="Saving...">Save</ModalButton>`
        },
        {
          title: 'With Icons',
          description: 'Buttons can include icons on either side of the text.',
          component: (
            <div className="flex gap-2">
              <ModalButton variant="primary" leftIcon={<Save size={16} />}>
                Save
              </ModalButton>
              <ModalButton variant="secondary" rightIcon={<ArrowRight size={16} />}>
                Next
              </ModalButton>
              <ModalButton variant="outline" leftIcon={<Download size={16} />} rightIcon={<ArrowRight size={16} />}>
                Download
              </ModalButton>
            </div>
          ),
          code: `<ModalButton variant="primary" leftIcon={<Save size={16} />}>
  Save
</ModalButton>
<ModalButton variant="secondary" rightIcon={<ArrowRight size={16} />}>
  Next
</ModalButton>
<ModalButton variant="outline" leftIcon={<Download size={16} />} rightIcon={<ArrowRight size={16} />}>
  Download
</ModalButton>`
        },
        {
          title: 'Full Width',
          description: 'Buttons can take up the full width of their container.',
          component: (
            <div className="w-full space-y-2">
              <ModalButton variant="primary" fullWidth>Full Width Button</ModalButton>
              <ModalButton variant="secondary" fullWidth>Full Width Button</ModalButton>
            </div>
          ),
          code: `<ModalButton variant="primary" fullWidth>Full Width Button</ModalButton>
<ModalButton variant="secondary" fullWidth>Full Width Button</ModalButton>`
        },
        {
          title: 'Disabled State',
          description: 'Buttons can be disabled to prevent user interaction.',
          component: (
            <div className="flex gap-2">
              <ModalButton variant="primary" disabled>Disabled</ModalButton>
              <ModalButton variant="outline" disabled>Disabled</ModalButton>
            </div>
          ),
          code: `<ModalButton variant="primary" disabled>Disabled</ModalButton>
<ModalButton variant="outline" disabled>Disabled</ModalButton>`
        }
      ]}
      bestPractices={{
        do: [
          'Use primary variant for the main action in a modal (e.g., confirm, save).',
          'Use secondary, outline, or ghost variants for cancel or less important actions.',
          'Use danger variant for destructive actions (e.g., delete, remove).',
          'Keep button text concise and action-oriented.',
          'Show loading states when an action will take time to complete.',
          'Match icon to the action the button performs for better usability.'
        ],
        dont: [
          'Don\'t use too many buttons with the same variant in a single modal.',
          'Don\'t use full width buttons unnecessarily in wide containers.',
          'Don\'t use primary buttons for destructive actions; use the danger variant instead.',
          'Don\'t disable buttons without providing context about why they\'re disabled.',
          'Don\'t add too many icons that might distract from the button\'s purpose.',
          'Don\'t use overly long button text that might overflow or wrap poorly.'
        ]
      }}
      accessibility={[
        'Buttons have appropriate color contrast ratios for all variants.',
        'Loading states are communicated visually with a spinner and textually.',
        'Disabled states have visual indicators to show the button is not interactive.',
        'Icons are paired with text to ensure their meaning is clear.',
        'Focus states are visually distinct with a ring outline.',
        'Button sizes provide adequate touch targets for mobile and tablet users.'
      ]}
    />
  );
};

export default ModalButtonDoc;