import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { Table, FileSpreadsheet, ArrowRight, FileX } from 'lucide-react';

// We'll simulate the SheetSelector component for documentation purposes
// since it requires file input and complex state management
const SheetSelectorDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Sheet Selector"
      description="A component for selecting and configuring Excel or CSV spreadsheet data, allowing users to choose a specific sheet and header row for data import."
      importCode={`import SheetSelector from '../components/ui/SheetSelector';`}
      usage={
        <div className="w-full p-4 border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-gray-800">
          <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
            <div className="flex items-center gap-2">
              <Table className="h-5 w-5 text-blue-500" />
              <span className="text-lg font-semibold">Configure Data Import</span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              1,250 rows • 12 columns
            </div>
          </div>
          
          <div className="flex gap-4 text-sm">
            <div className="w-60 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Sheet
                </label>
                <select className="w-full rounded border border-gray-300 dark:border-gray-600 p-2 bg-white dark:bg-gray-800">
                  <option>Sheet1</option>
                  <option>Data</option>
                  <option>Summary</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Header Row
                </label>
                <input 
                  type="number" 
                  value="1" 
                  className="w-20 rounded border border-gray-300 dark:border-gray-600 p-2 bg-white dark:bg-gray-800" 
                />
              </div>
            </div>
            
            <div className="flex-1">
              <div className="border border-gray-200 dark:border-gray-700 rounded overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    <tr className="bg-blue-50 dark:bg-blue-900/30">
                      <td className="w-12 pl-4 pr-2 py-2">
                        <div className="w-5 h-5 rounded bg-blue-500 flex items-center justify-center">
                          <svg className="h-3 w-3 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </td>
                      <td className="px-2 py-2 text-xs text-gray-500">Row 1</td>
                      <td className="px-3 py-2 font-medium">Name</td>
                      <td className="px-3 py-2 font-medium">Email</td>
                      <td className="px-3 py-2 font-medium">Status</td>
                      <td className="px-3 py-2 font-medium">Created At</td>
                    </tr>
                    <tr>
                      <td className="w-12 pl-4 pr-2 py-2">
                        <div className="w-5 h-5 rounded border border-gray-300 dark:border-gray-600"></div>
                      </td>
                      <td className="px-2 py-2 text-xs text-gray-500">Row 2</td>
                      <td className="px-3 py-2">John Doe</td>
                      <td className="px-3 py-2"><EMAIL></td>
                      <td className="px-3 py-2">Active</td>
                      <td className="px-3 py-2">2025-01-15</td>
                    </tr>
                    <tr>
                      <td className="w-12 pl-4 pr-2 py-2">
                        <div className="w-5 h-5 rounded border border-gray-300 dark:border-gray-600"></div>
                      </td>
                      <td className="px-2 py-2 text-xs text-gray-500">Row 3</td>
                      <td className="px-3 py-2">Jane Smith</td>
                      <td className="px-3 py-2"><EMAIL></td>
                      <td className="px-3 py-2">Inactive</td>
                      <td className="px-3 py-2">2025-02-20</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button className="px-3 py-2 text-sm rounded text-gray-600 dark:text-gray-400 mr-3">
              Cancel
            </button>
            <button className="px-3 py-2 text-sm rounded text-white bg-blue-600 dark:bg-blue-500">
              Continue with Selection
            </button>
          </div>
        </div>
      }
      code={`import { useState } from 'react';
import SheetSelector from '../components/ui/SheetSelector';

function DataImportForm() {
  const [file, setFile] = useState(null);
  const [sheetSelection, setSheetSelection] = useState(null);
  
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };
  
  const handleSheetSelect = (selection) => {
    // selection contains: { sheetName, headerRow, headers }
    setSheetSelection(selection);
    
    // Proceed with data import using the selected sheet configuration
    if (selection) {
      console.log('Importing data from sheet:', selection.sheetName);
      console.log('Using header row:', selection.headerRow);
      console.log('Headers:', selection.headers);
    }
  };
  
  return (
    <div>
      {!file ? (
        <div className="p-4 border border-dashed border-gray-300 rounded">
          <input 
            type="file" 
            accept=".xlsx,.xls,.csv" 
            onChange={handleFileChange} 
          />
        </div>
      ) : (
        <SheetSelector 
          file={file} 
          onSheetSelect={handleSheetSelect} 
        />
      )}
    </div>
  );
}`}
      props={[
        {
          name: 'file',
          type: 'File',
          description: 'The Excel or CSV file object to be processed.',
          required: true
        },
        {
          name: 'onSheetSelect',
          type: '(selection: { sheetName: string; headerRow: number; headers: string[] } | null) => void',
          description: 'Callback function that receives the selected sheet configuration or null when cancelled.',
          required: true
        }
      ]}
      variants={[
        {
          title: 'Standard Sheet Selector',
          description: 'The standard implementation of the sheet selector component showing a table preview.',
          component: (
            <div className="w-full p-4 border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-gray-800">
              <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                <div className="flex items-center gap-2">
                  <Table className="h-5 w-5 text-blue-500" />
                  <span className="text-lg font-semibold">Configure Data Import</span>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  1,250 rows • 12 columns
                </div>
              </div>
              
              <div className="flex gap-4 text-sm">
                <div className="w-60 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Sheet
                    </label>
                    <select className="w-full rounded border border-gray-300 dark:border-gray-600 p-2 bg-white dark:bg-gray-800">
                      <option>Sheet1</option>
                      <option>Data</option>
                      <option>Summary</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Header Row
                    </label>
                    <input 
                      type="number" 
                      value="1" 
                      className="w-20 rounded border border-gray-300 dark:border-gray-600 p-2 bg-white dark:bg-gray-800" 
                      readOnly
                    />
                  </div>
                </div>
                
                <div className="flex-1">
                  <div className="border border-gray-200 dark:border-gray-700 rounded overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        <tr className="bg-blue-50 dark:bg-blue-900/30">
                          <td className="w-12 pl-4 pr-2 py-2">
                            <div className="w-5 h-5 rounded bg-blue-500 flex items-center justify-center">
                              <svg className="h-3 w-3 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </div>
                          </td>
                          <td className="px-2 py-2 text-xs text-gray-500">Row 1</td>
                          <td className="px-3 py-2 font-medium">Name</td>
                          <td className="px-3 py-2 font-medium">Email</td>
                          <td className="px-3 py-2 font-medium">Status</td>
                          <td className="px-3 py-2 font-medium">Created At</td>
                        </tr>
                        <tr>
                          <td className="w-12 pl-4 pr-2 py-2">
                            <div className="w-5 h-5 rounded border border-gray-300 dark:border-gray-600"></div>
                          </td>
                          <td className="px-2 py-2 text-xs text-gray-500">Row 2</td>
                          <td className="px-3 py-2">John Doe</td>
                          <td className="px-3 py-2"><EMAIL></td>
                          <td className="px-3 py-2">Active</td>
                          <td className="px-3 py-2">2025-01-15</td>
                        </tr>
                        <tr>
                          <td className="w-12 pl-4 pr-2 py-2">
                            <div className="w-5 h-5 rounded border border-gray-300 dark:border-gray-600"></div>
                          </td>
                          <td className="px-2 py-2 text-xs text-gray-500">Row 3</td>
                          <td className="px-3 py-2">Jane Smith</td>
                          <td className="px-3 py-2"><EMAIL></td>
                          <td className="px-3 py-2">Inactive</td>
                          <td className="px-3 py-2">2025-02-20</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button className="px-3 py-2 text-sm rounded text-gray-600 dark:text-gray-400 mr-3">
                  Cancel
                </button>
                <button className="px-3 py-2 text-sm rounded text-white bg-blue-600 dark:bg-blue-500">
                  Continue with Selection
                </button>
              </div>
            </div>
          ),
          code: `<SheetSelector 
  file={excelFile} 
  onSheetSelect={handleSheetSelect} 
/>`
        },
        {
          title: 'Loading State',
          description: 'The component displays a loading state when initializing or processing the workbook.',
          component: (
            <div className="bg-white dark:bg-gray-800 rounded shadow-lg border border-gray-200 dark:border-gray-700 p-12">
              <div className="flex flex-col items-center justify-center gap-4">
                <svg className="h-8 w-8 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className="text-sm text-gray-600 dark:text-gray-400">Loading workbook...</p>
              </div>
            </div>
          ),
          code: `// The SheetSelector will automatically show this state when loading
<SheetSelector 
  file={largeExcelFile} 
  onSheetSelect={handleSheetSelect} 
/>`
        },
        {
          title: 'Empty State',
          description: 'The component gracefully handles empty sheets or files with errors.',
          component: (
            <div className="w-full p-4 border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-gray-800">
              <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                <div className="flex items-center gap-2">
                  <FileX className="h-5 w-5 text-red-500" />
                  <span className="text-lg font-semibold">Configure Data Import</span>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  0 rows • 0 columns
                </div>
              </div>
              
              <div className="flex gap-4 text-sm">
                <div className="w-60 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Sheet
                    </label>
                    <select className="w-full rounded border border-gray-300 dark:border-gray-600 p-2 bg-white dark:bg-gray-800">
                      <option>Sheet1</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Header Row
                    </label>
                    <input 
                      type="number" 
                      value="1" 
                      className="w-20 rounded border border-gray-300 dark:border-gray-600 p-2 bg-white dark:bg-gray-800" 
                      readOnly
                    />
                  </div>
                </div>
                
                <div className="flex-1">
                  <div className="border border-gray-200 dark:border-gray-700 rounded overflow-hidden">
                    <div className="p-8 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                      <FileSpreadsheet className="h-12 w-12 mb-2" />
                      <p className="text-sm font-medium">No data available</p>
                      <p className="text-xs mt-1">The selected sheet contains no data or could not be parsed correctly.</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button className="px-3 py-2 text-sm rounded text-gray-600 dark:text-gray-400 mr-3">
                  Cancel
                </button>
                <button className="px-3 py-2 text-sm rounded text-white bg-blue-600 dark:bg-blue-500" disabled>
                  Continue with Selection
                </button>
              </div>
            </div>
          ),
          code: `// The SheetSelector will handle empty files gracefully
<SheetSelector 
  file={emptyOrErrorFile} 
  onSheetSelect={handleSheetSelect} 
/>`
        }
      ]}
      bestPractices={{
        do: [
          'Use when importing data from Excel or CSV files to allow users to configure import settings.',
          'Allow users to select from multiple sheets when available.',
          'Provide a clear visual indicator for the selected header row.',
          'Show a preview of the data to help users make informed decisions.',
          'Include clear statistics about the data being imported (rows, columns).',
          'Handle large files efficiently with progressive loading.'
        ],
        dont: [
          'Don\'t use for simple CSV imports where there\'s only one sheet and the header row is always the first row.',
          'Don\'t automatically proceed with import without user confirmation.',
          'Don\'t fail silently when file parsing errors occur; provide clear feedback.',
          'Don\'t load entire large files into memory at once; use chunked loading for better performance.',
          'Don\'t limit users to just the most recent Excel formats; support legacy formats (.xls) as well.'
        ]
      }}
      accessibility={[
        'Ensure all form controls (sheet selector, header row input) are properly labeled.',
        'Provide keyboard navigation support for selecting rows as headers.',
        'Include clear visual indicators for the current selection state.',
        'Use appropriate ARIA attributes for interactive elements.',
        'Ensure error states are communicated both visually and through assistive technologies.',
        'Provide loading indicators for operations that may take time to complete.',
        'Maintain good color contrast for all UI elements in both light and dark themes.'
      ]}
    />
  );
};

export default SheetSelectorDoc;