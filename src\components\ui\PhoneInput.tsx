import React, { useState, useEffect } from 'react';
import clsx from 'clsx';

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
}

export const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  placeholder = '+****************',
  className = '',
  error,
  required = false,
  disabled = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [formattedValue, setFormattedValue] = useState('');

  // Format the phone number for display
  useEffect(() => {
    // Only format if there's a value
    if (value) {
      // Keep the + at the beginning if it exists
      const hasPlus = value.startsWith('+');
      
      // Remove all non-digit characters except the + at the beginning
      let digitsOnly = value.replace(/[^\d+]/g, '');
      if (hasPlus && !digitsOnly.startsWith('+')) {
        digitsOnly = '+' + digitsOnly;
      } else if (!hasPlus && digitsOnly.startsWith('+')) {
        digitsOnly = digitsOnly.substring(1);
      }
      
      setFormattedValue(digitsOnly);
    } else {
      setFormattedValue('');
    }
  }, [value]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    
    // Allow only digits, +, and the first character can be +
    const sanitized = input.replace(/[^\d+]/g, '');
    
    // Ensure + is only at the beginning
    const formatted = sanitized.replace(/\+/g, (match, offset) => offset === 0 ? '+' : '');
    
    onChange(formatted);
  };

  return (
    <div className={clsx('relative', className)}>
      <input
        type="tel"
        value={formattedValue}
        onChange={handleChange}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className={clsx(
          'w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 transition-all',
          {
            'border-gray-300 focus:border-blue-500 focus:ring-blue-200 dark:border-gray-600 dark:bg-gray-700 dark:text-white': !error,
            'border-red-500 focus:border-red-500 focus:ring-red-200 dark:border-red-500': error,
            'bg-gray-100 dark:bg-gray-800': disabled,
          }
        )}
      />
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error}</p>
      )}
    </div>
  );
};
