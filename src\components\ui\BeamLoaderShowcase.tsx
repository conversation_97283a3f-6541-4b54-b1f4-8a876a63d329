import React, { useState } from 'react';
import { LoadingSpinner } from '../LoadingSpinner';
import { BeamOutlineLoader } from './BeamOutlineLoader';

const BeamLoaderShowcase: React.FC = () => {
  const [darkMode, setDarkMode] = useState(false);
  
  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'} p-8`}>
      <header className="max-w-4xl mx-auto mb-12 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-2">Beam Outline Loader</h1>
          <p className="text-lg opacity-80">Animated beam that traces the outlines of the Resultid logo shapes</p>
        </div>
        <button 
          onClick={() => setDarkMode(!darkMode)}
          className={`p-2 rounded-full ${darkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'}`}
        >
          {darkMode ? '☀️' : '🌙'}
        </button>
      </header>
      
      <div className="max-w-4xl mx-auto">
        <div className={`p-12 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-gray-50'} mb-12`}>
          <div className="flex justify-center items-center mb-8">
            <BeamOutlineLoader size="lg" />
          </div>
          <div className="text-center">
            <h2 className="text-xl font-bold mb-2">Premium Beam Animation</h2>
            <p className="max-w-xl mx-auto opacity-80">
              An elegant loading animation that follows the outline of the diamond shapes in the Resultid logo. 
              A white beam traces each shape in sequence, creating a premium, branded loading experience.
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className={`p-8 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-gray-50'} flex flex-col items-center`}>
            <BeamOutlineLoader size="sm" className="mb-4" />
            <span>Small</span>
          </div>
          <div className={`p-8 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-gray-50'} flex flex-col items-center`}>
            <BeamOutlineLoader size="md" className="mb-4" />
            <span>Medium</span>
          </div>
          <div className={`p-8 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-gray-50'} flex flex-col items-center`}>
            <BeamOutlineLoader size="lg" className="mb-4" />
            <span>Large</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className={`p-8 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
            <div className="h-40 flex flex-col items-center justify-center">
              <div className={`w-full max-w-sm p-6 rounded-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} shadow-lg`}>
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 rounded-full bg-gray-300 animate-pulse"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded animate-pulse mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded animate-pulse w-2/3"></div>
                  </div>
                </div>
                <div className="h-24 bg-gray-300 rounded animate-pulse mb-4"></div>
                <div className="flex justify-center">
                  <LoadingSpinner size="sm" />
                </div>
              </div>
            </div>
            <p className="mt-4 text-center opacity-80">Card Loading State</p>
          </div>
          
          <div className={`p-8 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
            <div className="h-40 flex flex-col items-center justify-center">
              <div className={`text-center ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                <LoadingSpinner size="md" className="mb-4 mx-auto" />
                <h3 className="text-xl font-bold mb-2">Analyzing results</h3>
                <p className="opacity-70">Our AI is processing your request</p>
              </div>
            </div>
            <p className="mt-4 text-center opacity-80">AI Processing State</p>
          </div>
        </div>
        
        <div className="text-center opacity-70 text-sm py-8">
          <p>© 2025 Resultid. Premium loading animations for AI applications.</p>
        </div>
      </div>
    </div>
  );
};

export default BeamLoaderShowcase;