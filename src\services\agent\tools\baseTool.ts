/**
 * Base tool interface for all agent tools
 * Defines the structure and requirements for all tools used by the agent
 */

export enum ToolCategory {
  DATA_NAVIGATOR = 'data_navigator',
  DATA_ANALYSIS = 'data_analysis',
  PLATFORM_NAVIGATION = 'platform_navigation',
  INTEGRATION = 'integration',
  CONTEXTUAL = 'contextual'
}

/**
 * Base interface for all agent tools
 * P: Parameter type that the tool accepts
 * R: Result type that the tool returns
 */
export interface BaseTool<P, R> {
  /** Unique identifier for the tool */
  id: string;
  
  /** Display name of the tool */
  name: string;
  
  /** Human-readable description */
  description: string;
  
  /** Grouping for organization */
  category: ToolCategory;
  
  /** Optional UI icon for display */
  icon?: string;
  
  /** Whether authentication is required */
  requiresAuth: boolean;
  
  /** Required permissions to use this tool */
  permissions: string[];
  
  /**
   * Core execution method that runs the tool
   * @param params Tool-specific parameters
   * @returns Promise resolving to the tool's result
   */
  execute(params: P): Promise<R>;
  
  /**
   * Validation for parameters
   * @param params Parameters to validate
   * @returns Whether the parameters are valid
   */
  validateParams(params: P): boolean;
  
  /**
   * Help text generation for tool usage
   * @returns String with helpful usage instructions
   */
  getHelpText(): string;
  
  /**
   * Examples of valid parameters
   * @returns Array of example parameter objects with descriptions
   */
  getExamples(): Array<{params: P, description: string}>;
}

/**
 * Base abstract class that implements common functionality for tools
 */
export abstract class BaseToolImplementation<P, R> implements BaseTool<P, R> {
  abstract id: string;
  abstract name: string;
  abstract description: string;
  abstract category: ToolCategory;
  abstract requiresAuth: boolean;
  abstract permissions: string[];
  
  abstract execute(params: P): Promise<R>;
  abstract validateParams(params: P): boolean;
  
  /**
   * Default implementation of getHelpText()
   * Can be overridden by subclasses
   */
  getHelpText(): string {
    const examples = this.getExamples();
    const exampleText = examples.length > 0 
      ? `\n\nExamples:\n${examples.map(ex => `- ${ex.description}`).join('\n')}`
      : '';
    
    return `${this.description}${exampleText}`;
  }
  
  /**
   * Default implementation of getExamples()
   * Should be overridden by subclasses with real examples
   */
  abstract getExamples(): Array<{params: P, description: string}>;
}

/**
 * Standard error format for tool execution failures
 */
export interface ToolExecutionError {
  /** Error type identifier */
  type: string;
  
  /** Human-readable error message */
  message: string;
  
  /** Optional additional details about the error */
  details?: any;
}

/**
 * Base result interface that all tool results should extend
 */
export interface BaseToolResult {
  /** Whether the tool execution was successful */
  success: boolean;
  
  /** Error information if success is false */
  error?: ToolExecutionError;
}

/**
 * Represents an active or completed tool execution
 */
export interface ToolExecution {
  /** ID of the tool that was executed */
  toolId: string;
  
  /** Current status of the execution */
  status: 'queued' | 'running' | 'complete' | 'error';
  
  /** Timestamp when execution started */
  startTime: number;
  
  /** Optional completion timestamp */
  endTime?: number;
  
  /** Result if execution is complete */
  result?: any;
  
  /** Error if execution failed */
  error?: Error;
}