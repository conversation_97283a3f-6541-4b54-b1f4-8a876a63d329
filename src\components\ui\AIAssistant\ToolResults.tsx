/**
 * Tool Results component
 * Displays the results of tool executions within the AI assistant interface
 */

import React, { useState } from 'react';
import { ToolExecution } from '../../../services/agent/tools/baseTool';
import UrlPreviewResult from './results/UrlPreviewResult';

interface ToolResultsProps {
  toolExecutions: Record<string, ToolExecution>;
  onInteract?: (action: string, toolId: string, data?: any) => void;
  compactMode?: boolean;
}

// Map tool IDs to specific renderers
const TOOL_RENDERERS: Record<string, React.ComponentType<any>> = {
  'url-metadata': UrlPreviewResult,
  // Add more tool renderers as they are implemented
};

/**
 * Renders tool execution results
 */
const ToolResults: React.FC<ToolResultsProps> = ({ 
  toolExecutions, 
  onInteract,
  compactMode = false
}) => {
  const [expandedTools, setExpandedTools] = useState<Record<string, boolean>>({});
  
  // Handle tool expansion toggle
  const toggleToolExpansion = (toolId: string) => {
    setExpandedTools(prev => ({
      ...prev,
      [toolId]: !prev[toolId]
    }));
  };
  
  // If no tool executions, render nothing
  if (Object.keys(toolExecutions).length === 0) {
    return null;
  }
  
  return (
    <div className="tool-results-container mt-2 mb-2">
      {Object.entries(toolExecutions).map(([toolId, execution]) => {
        const ToolRenderer = TOOL_RENDERERS[toolId];
        const isExpanded = expandedTools[toolId] ?? !compactMode;
        
        return (
          <div key={toolId} className="tool-result mb-2">
            <div className="tool-header flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-800 rounded-t-md">
              <div className="tool-info flex items-center">
                <span className="tool-status mr-2">
                  {execution.status === 'complete' && (
                    <span className="text-green-500">✓</span>
                  )}
                  {execution.status === 'error' && (
                    <span className="text-red-500">✗</span>
                  )}
                  {(execution.status === 'running' || execution.status === 'queued') && (
                    <span className="text-blue-500 animate-pulse">⋯</span>
                  )}
                </span>
                <span className="tool-name font-medium">
                  {toolId.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </span>
              </div>
              
              <button 
                className="expand-toggle text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                onClick={() => toggleToolExpansion(toolId)}
                aria-label={isExpanded ? "Collapse" : "Expand"}
              >
                {isExpanded ? '▲' : '▼'}
              </button>
            </div>
            
            {isExpanded && (
              <div className="tool-content p-2 border border-gray-200 dark:border-gray-700 rounded-b-md">
                {execution.status === 'complete' && ToolRenderer ? (
                  <ToolRenderer 
                    result={execution.result} 
                    onInteract={(action: string, data?: any) => 
                      onInteract?.(action, toolId, data)
                    } 
                  />
                ) : execution.status === 'error' ? (
                  <div className="tool-error text-red-500">
                    <p>Error: {execution.error?.message || 'An unknown error occurred'}</p>
                  </div>
                ) : (
                  <div className="tool-loading flex justify-center items-center py-4">
                    <div className="animate-pulse flex space-x-4">
                      <div className="flex-1 space-y-4 py-1">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ToolResults;