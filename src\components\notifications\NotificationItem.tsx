import React, { useEffect, useState } from "react";
import { Notification } from "../../types/notificationTypes";

interface NotificationItemProps {
  notification: Notification;
  onDismiss: (id: string) => void;
}

export const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onDismiss,
}) => {
  const [isExiting, setIsExiting] = useState(false);

  // Handle auto-dismiss for toast notifications only
  // This should only apply to toast notifications, not notifications in the history
  useEffect(() => {
    // Only auto-dismiss if this is a toast notification (not in history)
    // and if it has a duration specified
    if (
      notification.duration !== null &&
      notification.duration !== undefined &&
      !notification.inHistory
    ) {
      // Skip auto-dismiss for history notifications

      console.log(
        `NotificationItem: Setting auto-dismiss for ${notification.id} after ${notification.duration}ms`
      );

      const timer = setTimeout(() => {
        console.log(`NotificationItem: Auto-dismissing ${notification.id}`);
        setIsExiting(true);
        // Add a small delay for exit animation
        setTimeout(() => onDismiss(notification.id), 300);
      }, notification.duration);

      return () => clearTimeout(timer);
    }
  }, [notification, onDismiss]);

  // Handle manual dismiss
  const handleDismiss = () => {
    setIsExiting(true);
    setTimeout(() => onDismiss(notification.id), 300);
  };

  // Handle keyboard navigation for accessibility
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      handleDismiss();
    }
  };

  // Get appropriate color based on notification type
  const getTypeStyles = () => {
    switch (notification.type) {
      case "success":
        return "bg-green-100 border-green-500 text-green-800 dark:bg-green-900/30 dark:border-green-800/50 dark:text-green-300";
      case "error":
        return "bg-red-100 border-red-500 text-red-800 dark:bg-red-900/30 dark:border-red-800/50 dark:text-red-300";
      case "warning":
        return "bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/30 dark:border-yellow-800/50 dark:text-yellow-300";
      case "info":
        return "bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/30 dark:border-blue-800/50 dark:text-blue-300";
      case "custom":
        return "bg-purple-100 border-purple-500 text-purple-800 dark:bg-purple-900/30 dark:border-purple-800/50 dark:text-purple-300";
      default:
        return "bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/30 dark:border-gray-800/50 dark:text-gray-300";
    }
  };

  // Render notification content based on its type
  const renderContent = () => {
    const { content } = notification;

    if (content.component) {
      return content.component;
    }

    if (content.html) {
      return <div dangerouslySetInnerHTML={{ __html: content.html }} />;
    }

    if (content.text) {
      return <p>{content.text}</p>;
    }

    if (content.image) {
      return (
        <img
          src={content.image}
          alt="Notification"
          className="max-w-full h-auto"
        />
      );
    }

    return null;
  };

  return (
    <div
      className={`
        ${getTypeStyles()}
        ${isExiting ? "animate-fade-out" : "animate-slide-in"}
        border-l-4 p-4 mb-3 rounded-md shadow-md flex items-start
        transition-all duration-300 ease-in-out
      `}
      role="alert"
      aria-live="assertive"
      aria-atomic="true"
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className="flex-grow">
        {notification.title && (
          <h4 className="font-semibold mb-1">{notification.title}</h4>
        )}
        <div className="notification-content">{renderContent()}</div>

        {notification.actions && notification.actions.length > 0 && (
          <div className="mt-2 flex gap-2">
            {notification.actions.map((action, index) => (
              <button
                key={index}
                onClick={action.onClick}
                className={`
                  px-3 py-1 rounded text-sm
                  ${
                    action.variant === "primary"
                      ? "bg-blue-500 text-white hover:bg-blue-600"
                      : action.variant === "secondary"
                      ? "bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
                      : "text-blue-600 hover:underline dark:text-blue-400"
                  }
                `}
              >
                {action.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {notification.dismissible && (
        <button
          onClick={handleDismiss}
          className="ml-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Dismiss"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </div>
  );
};
