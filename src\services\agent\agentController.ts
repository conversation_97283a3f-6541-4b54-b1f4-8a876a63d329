/**
 * Agent Controller
 * Implements the core agent loop for processing messages, executing tools, and generating responses
 */

import { AIMessage, ContextItem } from '../../components/ui/AIAssistant/types';
import toolRegistry from './toolRegistry';
import { BaseTool, ToolExecution, BaseToolResult } from './tools/baseTool';
import * as llmApi from '../llmApi';

/**
 * Context information for the agent
 */
export interface AgentContext {
  /** Previous messages in the conversation */
  messages: AIMessage[];
  
  /** Context items from the UI */
  contextItems?: ContextItem[];
  
  /** Type of context (tracker, insights, etc.) */
  contextType: 'tracker' | 'insights' | 'campaign' | 'general';
  
  /** Entity ID related to the conversation */
  entityId?: string;
  
  /** Additional context data */
  contextData?: Record<string, unknown>;
  
  /** User permissions */
  userPermissions?: string[];
}

/**
 * Intent analysis result
 */
interface IntentAnalysis {
  /** Primary intent of the user message */
  primaryIntent: string;
  
  /** Confidence score for the intent (0-1) */
  confidence: number;
  
  /** Entity extractions from the message */
  entities: Record<string, any>;
  
  /** Whether tools should be used */
  requiresTools: boolean;
  
  /** Suggested tools to use */
  suggestedTools: string[];
}

/**
 * Tool execution plan
 */
interface ExecutionPlan {
  /** Tools to execute */
  tools: Array<{
    /** Tool ID */
    toolId: string;
    
    /** Parameters to use */
    parameters: any;
    
    /** Reason for using this tool */
    reason: string;
  }>;
  
  /** Why this plan was chosen */
  reasoning: string;
}

/**
 * Observation from tool execution
 */
interface ToolObservation {
  /** Tool that was executed */
  toolId: string;
  
  /** Execution success status */
  success: boolean;
  
  /** Result data if successful */
  result?: any;
  
  /** Error information if failed */
  error?: Error;
  
  /** Execution time in milliseconds */
  executionTime: number;
}

/**
 * Reflection on tool results
 */
interface Reflection {
  /** Whether the tools satisfied the user's request */
  requirementsSatisfied: boolean;
  
  /** Missing information or actions */
  missingInfo: string[];
  
  /** Next steps to take */
  nextSteps: string[];
  
  /** Key insights from the results */
  insights: string[];
}

/**
 * Agent response to the user
 */
export interface AgentResponse {
  /** AI message response */
  message: AIMessage;
  
  /** Tools that were used */
  tools?: ToolExecution[];
  
  /** Whether the intent was fully satisfied */
  intentSatisfied: boolean;
  
  /** Suggested follow-up actions */
  suggestedFollowUps?: string[];
}

/**
 * Controller for the agent loop
 */
export class AgentController {
  private static instance: AgentController | null = null;
  
  /**
   * Get the singleton instance of the agent controller
   */
  public static getInstance(): AgentController {
    if (!AgentController.instance) {
      AgentController.instance = new AgentController();
    }
    return AgentController.instance;
  }
  
  /**
   * Process a user message through the agent loop
   * @param message User message content
   * @param context Conversation context
   * @returns Agent response
   */
  public async processMessage(message: string, context: AgentContext): Promise<AgentResponse> {
    // 1. Analysis Phase - Understand user intent
    const intent = await this.analyzeIntent(message, context);
    
    // If no tools are required, return direct LLM response
    if (!intent.requiresTools) {
      return await this.generateDirectResponse(message, context);
    }
    
    // 2. Planning Phase - Select appropriate tools
    const plan = await this.createPlan(intent, context);
    
    // 3. Execution Phase - Run selected tools
    const observations = await this.executeTools(plan.tools);
    
    // 4. Reflection Phase - Determine next steps
    const reflection = await this.reflect(observations, intent, context);
    
    // 5. Communication Phase - Format response to user
    return await this.formatResponse(reflection, observations, message, context);
  }
  
  /**
   * Analyze the user's intent
   * @param message User message
   * @param context Conversation context
   * @returns Intent analysis
   */
  private async analyzeIntent(message: string, context: AgentContext): Promise<IntentAnalysis> {
    try {
      // For initial implementation, we'll use a simple prompt-based approach
      // In future iterations, this could be a dedicated API endpoint
      
      const availableTools = toolRegistry.getAllTools();
      const toolDescriptions = toolRegistry.formatToolsForPrompt(availableTools);
      
      const systemPrompt = `You are analyzing a user message to determine:
1. The primary intent
2. Whether tools should be used to fulfill this intent
3. Which specific tools would be most appropriate

Available tools:
${toolDescriptions}

Respond with a JSON object containing:
{
  "primaryIntent": "brief description of user intent",
  "confidence": 0.8, // confidence level between 0-1
  "entities": {}, // entities extracted from the message
  "requiresTools": true/false, // whether tools are needed
  "suggestedTools": ["tool-id-1", "tool-id-2"] // IDs of suggested tools
}`;

      const recentMessages = context.messages.slice(-5);
      
      const response = await llmApi.quickPrompt(
        systemPrompt,
        `User message: "${message}"
Recent conversation: ${JSON.stringify(recentMessages)}`,
        { parseJson: true }
      );
      
      // In case the JSON parsing in quickPrompt fails, try again
      let intentAnalysis: IntentAnalysis;
      if (typeof response === 'string') {
        try {
          // Extract JSON if it's wrapped in code blocks or text
          const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) || 
                            response.match(/```([\s\S]*?)```/) || 
                            response.match(/(\{[\s\S]*\})/);
          
          if (jsonMatch && jsonMatch[1]) {
            intentAnalysis = JSON.parse(jsonMatch[1]);
          } else {
            throw new Error('No JSON found in response');
          }
        } catch (err) {
          // Fallback intent analysis if parsing fails
          intentAnalysis = {
            primaryIntent: 'general_query',
            confidence: 0.5,
            entities: {},
            requiresTools: false,
            suggestedTools: []
          };
        }
      } else {
        intentAnalysis = response as IntentAnalysis;
      }
      
      return intentAnalysis;
    } catch (error) {
      console.error('Error in intent analysis:', error);
      
      // Return a fallback intent analysis
      return {
        primaryIntent: 'general_query',
        confidence: 0.5,
        entities: {},
        requiresTools: false,
        suggestedTools: []
      };
    }
  }
  
  /**
   * Create an execution plan for tools
   * @param intent Intent analysis
   * @param context Conversation context
   * @returns Execution plan
   */
  private async createPlan(intent: IntentAnalysis, context: AgentContext): Promise<ExecutionPlan> {
    try {
      // Get all suggested tools
      const suggestedTools = intent.suggestedTools
        .map(toolId => toolRegistry.getTool(toolId))
        .filter(Boolean) as BaseTool<any, any>[];
      
      if (suggestedTools.length === 0) {
        // No valid tools suggested
        return {
          tools: [],
          reasoning: 'No suitable tools available for this request.'
        };
      }
      
      // For initial implementation, we'll use prompt-based planning
      const toolDescriptions = toolRegistry.formatToolsForPrompt(suggestedTools);
      
      const systemPrompt = `You are creating an execution plan for tools to fulfill a user's request.
Based on the user intent and available tools, determine:
1. Which specific tools to use
2. The correct parameters for each tool
3. The execution order, if multiple tools are needed

Available tools:
${toolDescriptions}

Respond with a JSON object containing:
{
  "tools": [
    {
      "toolId": "tool-id",
      "parameters": {}, // parameters for the tool
      "reason": "why this tool was chosen"
    }
  ],
  "reasoning": "overall explanation of the plan"
}`;

      const recentMessages = context.messages.slice(-5);
      
      const response = await llmApi.quickPrompt(
        systemPrompt,
        `User intent: ${intent.primaryIntent}
Extracted entities: ${JSON.stringify(intent.entities)}
Recent conversation: ${JSON.stringify(recentMessages)}`,
        { parseJson: true }
      );
      
      // Handle response parsing
      let executionPlan: ExecutionPlan;
      if (typeof response === 'string') {
        try {
          // Extract JSON if it's wrapped in code blocks or text
          const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) || 
                           response.match(/```([\s\S]*?)```/) || 
                           response.match(/(\{[\s\S]*\})/);
          
          if (jsonMatch && jsonMatch[1]) {
            executionPlan = JSON.parse(jsonMatch[1]);
          } else {
            throw new Error('No JSON found in response');
          }
        } catch (err) {
          // Fallback with first suggested tool if parsing fails
          const firstTool = suggestedTools[0];
          executionPlan = {
            tools: [{
              toolId: firstTool.id,
              parameters: {},
              reason: `Using ${firstTool.name} to address the user's request.`
            }],
            reasoning: 'Using the most relevant tool based on user intent.'
          };
        }
      } else {
        executionPlan = response as ExecutionPlan;
      }
      
      return executionPlan;
    } catch (error) {
      console.error('Error in execution planning:', error);
      
      // Return a fallback plan
      return {
        tools: [],
        reasoning: 'Unable to create execution plan due to an error.'
      };
    }
  }
  
  /**
   * Execute tools according to the plan
   * @param toolPlans Tool execution plans
   * @returns Tool observations
   */
  private async executeTools(
    toolPlans: Array<{toolId: string; parameters: any; reason: string}>
  ): Promise<ToolObservation[]> {
    const observations: ToolObservation[] = [];
    
    // Execute each tool in sequence
    // In future versions, this could be parallelized when tools are independent
    for (const plan of toolPlans) {
      const tool = toolRegistry.getTool(plan.toolId);
      
      if (!tool) {
        observations.push({
          toolId: plan.toolId,
          success: false,
          error: new Error(`Tool not found: ${plan.toolId}`),
          executionTime: 0
        });
        continue;
      }
      
      // Validate parameters
      if (!tool.validateParams(plan.parameters)) {
        observations.push({
          toolId: plan.toolId,
          success: false,
          error: new Error(`Invalid parameters for tool: ${plan.toolId}`),
          executionTime: 0
        });
        continue;
      }
      
      // Execute the tool with timing
      const startTime = Date.now();
      try {
        const result = await tool.execute(plan.parameters);
        const endTime = Date.now();
        
        observations.push({
          toolId: plan.toolId,
          success: true,
          result,
          executionTime: endTime - startTime
        });
      } catch (error) {
        const endTime = Date.now();
        
        observations.push({
          toolId: plan.toolId,
          success: false,
          error: error as Error,
          executionTime: endTime - startTime
        });
      }
    }
    
    return observations;
  }
  
  /**
   * Reflect on tool observations
   * @param observations Tool execution observations
   * @param intent Original intent analysis
   * @param context Conversation context
   * @returns Reflection on results
   */
  private async reflect(
    observations: ToolObservation[],
    intent: IntentAnalysis,
    context: AgentContext
  ): Promise<Reflection> {
    try {
      // Format observations for prompt
      const observationsText = observations.map(obs => {
        if (obs.success) {
          return `Tool: ${obs.toolId}\nStatus: Success\nResult: ${JSON.stringify(obs.result)}\nExecution Time: ${obs.executionTime}ms`;
        } else {
          return `Tool: ${obs.toolId}\nStatus: Failed\nError: ${obs.error?.message}\nExecution Time: ${obs.executionTime}ms`;
        }
      }).join('\n\n');
      
      const systemPrompt = `You are reflecting on the results of tool executions to determine:
1. Whether the user's request has been satisfied
2. What information might still be missing
3. What next steps should be taken
4. Key insights from the results

Respond with a JSON object containing:
{
  "requirementsSatisfied": true/false,
  "missingInfo": ["description of missing information"],
  "nextSteps": ["suggested next actions"],
  "insights": ["key insights from the results"]
}`;

      const response = await llmApi.quickPrompt(
        systemPrompt,
        `User intent: ${intent.primaryIntent}
Tool observations:
${observationsText}`,
        { parseJson: true }
      );
      
      // Parse the response
      let reflection: Reflection;
      if (typeof response === 'string') {
        try {
          // Extract JSON if it's wrapped in code blocks or text
          const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) || 
                           response.match(/```([\s\S]*?)```/) || 
                           response.match(/(\{[\s\S]*\})/);
          
          if (jsonMatch && jsonMatch[1]) {
            reflection = JSON.parse(jsonMatch[1]);
          } else {
            throw new Error('No JSON found in response');
          }
        } catch (err) {
          // Fallback reflection if parsing fails
          const anySuccessful = observations.some(obs => obs.success);
          reflection = {
            requirementsSatisfied: anySuccessful,
            missingInfo: anySuccessful ? [] : ["Tool execution failed"],
            nextSteps: anySuccessful ? [] : ["Try a different approach"],
            insights: []
          };
        }
      } else {
        reflection = response as Reflection;
      }
      
      return reflection;
    } catch (error) {
      console.error('Error in reflection:', error);
      
      // Return a fallback reflection
      const anySuccessful = observations.some(obs => obs.success);
      return {
        requirementsSatisfied: anySuccessful,
        missingInfo: anySuccessful ? [] : ["Tool execution failed"],
        nextSteps: anySuccessful ? [] : ["Try a different approach"],
        insights: []
      };
    }
  }
  
  /**
   * Format a response based on reflection and observations
   * @param reflection Reflection on results
   * @param observations Tool observations
   * @param userMessage Original user message
   * @param context Conversation context
   * @returns Formatted response
   */
  private async formatResponse(
    reflection: Reflection,
    observations: ToolObservation[],
    userMessage: string,
    context: AgentContext
  ): Promise<AgentResponse> {
    try {
      // Format observations for prompt
      const observationsText = observations.map(obs => {
        if (obs.success) {
          return `Tool: ${obs.toolId}\nStatus: Success\nResult: ${JSON.stringify(obs.result)}\nExecution Time: ${obs.executionTime}ms`;
        } else {
          return `Tool: ${obs.toolId}\nStatus: Failed\nError: ${obs.error?.message}\nExecution Time: ${obs.executionTime}ms`;
        }
      }).join('\n\n');
      
      const systemPrompt = `You are an AI assistant creating a helpful response to a user based on:
1. Their original question
2. Results from tools that were executed
3. Reflection on whether their needs were met

Your response should be clear, helpful and directly reference the tool results where appropriate.
Do not include technical details about the tools themselves unless relevant to the user's understanding.`;

      const prompt = `User question: "${userMessage}"

Tool observations:
${observationsText}

Reflection:
- Requirements satisfied: ${reflection.requirementsSatisfied ? 'Yes' : 'No'}
- Missing information: ${reflection.missingInfo.join(', ') || 'None'}
- Next steps: ${reflection.nextSteps.join(', ') || 'None'}
- Insights: ${reflection.insights.join(', ') || 'None'}

Please provide a helpful response that addresses the user's question based on the tool results.`;

      const responseText = await llmApi.quickPrompt(systemPrompt, prompt, { parseJson: false });
      
      // Convert tool observations to tool executions
      const toolExecutions: ToolExecution[] = observations.map(obs => ({
        toolId: obs.toolId,
        status: obs.success ? 'complete' : 'error',
        startTime: Date.now() - obs.executionTime,
        endTime: Date.now(),
        result: obs.success ? obs.result : undefined,
        error: obs.success ? undefined : obs.error
      }));
      
      // Create the AI message
      const message: AIMessage = {
        type: 'ai',
        content: responseText,
        timestamp: new Date().toISOString(),
        toolsUsed: observations.map(obs => obs.toolId)
      };
      
      return {
        message,
        tools: toolExecutions,
        intentSatisfied: reflection.requirementsSatisfied,
        suggestedFollowUps: reflection.nextSteps.length > 0 ? reflection.nextSteps : undefined
      };
    } catch (error) {
      console.error('Error formatting response:', error);
      
      // Return a fallback response
      return {
        message: {
          type: 'ai',
          content: 'I processed your request, but encountered an error formatting the response. ' +
                  'Some tools were executed, but I was unable to properly analyze the results. ' +
                  'Please try again or rephrase your question.',
          timestamp: new Date().toISOString(),
          toolsUsed: observations.map(obs => obs.toolId)
        },
        intentSatisfied: false
      };
    }
  }
  
  /**
   * Generate a direct response without using tools
   * @param message User message
   * @param context Conversation context
   * @returns Direct response
   */
  private async generateDirectResponse(message: string, context: AgentContext): Promise<AgentResponse> {
    try {
      // For direct responses, we'll use the regular chat API
      // This bypasses the agent loop when tools aren't needed
      
      // Create user message
      const userMessage: AIMessage = {
        type: 'user',
        content: message.trim(),
        timestamp: new Date().toISOString()
      };
      
      // Use the llmApi to get a response
      const response = await llmApi.sendChat({
        messages: [...context.messages, userMessage],
        contextType: context.contextType,
        contextData: context.contextData,
        contextItems: context.contextItems,
        entityId: context.entityId
      });
      
      return {
        message: response.message,
        intentSatisfied: true
      };
    } catch (error) {
      console.error('Error generating direct response:', error);
      
      // Return a fallback response
      return {
        message: {
          type: 'ai',
          content: 'I encountered an error processing your request. Please try again or rephrase your question.',
          timestamp: new Date().toISOString()
        },
        intentSatisfied: false
      };
    }
  }
}

// Export singleton instance
export default AgentController.getInstance();