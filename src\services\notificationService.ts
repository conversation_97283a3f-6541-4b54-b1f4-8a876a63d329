import { useNotification } from "../contexts/NotificationContext";
import {
  NotificationType,
  NotificationPriority,
  NotificationContent,
  NotificationAction,
  Notification,
} from "../types/notificationTypes";

// Default durations for different notification types (in milliseconds)
const DEFAULT_DURATIONS = {
  success: 5000,
  error: 8000,
  info: 5000,
  warning: 7000,
  custom: 5000,
};

// Maximum number of visible notifications for queuing
const MAX_VISIBLE_NOTIFICATIONS = 3;

export const useNotificationService = () => {
  const {
    notifications,
    addNotification,
    removeNotification,
    updateNotification,
    clearAllNotifications,
    markAsRead,
  } = useNotification();

  // Notification queue for high-volume scenarios
  const notificationQueue: Notification[] = [];

  // Process the notification queue
  const processQueue = () => {
    if (
      notificationQueue.length > 0 &&
      notifications.length < MAX_VISIBLE_NOTIFICATIONS
    ) {
      const nextNotification = notificationQueue.shift();
      if (nextNotification) {
        addNotification(nextNotification);
      }
    }
  };

  // Queue or show notification based on current count
  const queueOrShowNotification = (
    notification: Omit<Notification, "id" | "timestamp" | "read">
  ) => {
    if (notifications.length >= MAX_VISIBLE_NOTIFICATIONS) {
      notificationQueue.push({
        ...notification,
        id: "queued-" + Date.now(),
        timestamp: Date.now(),
        read: false,
      });
      // Set up a timer to process the queue
      setTimeout(processQueue, 300);
    } else {
      return addNotification(notification);
    }
    return "";
  };

  // Group similar notifications
  const groupSimilarNotifications = (
    newNotification: Omit<Notification, "id" | "timestamp" | "read">
  ): string => {
    // Find similar notifications (e.g., same type and similar content)
    const similarNotification = notifications.find(
      (n) =>
        n.type === newNotification.type &&
        n.content.text === newNotification.content.text
    );

    if (similarNotification) {
      // Update the existing notification with a count
      const count = (similarNotification.metadata?.count || 1) + 1;
      updateNotification(similarNotification.id, {
        content: {
          ...similarNotification.content,
          text: `${
            similarNotification.content.text?.split(" (")[0]
          } (${count})`,
        },
        metadata: { ...similarNotification.metadata, count },
      });
      return similarNotification.id;
    }

    // No similar notification found, add the new one
    return addNotification({
      ...newNotification,
      metadata: { ...newNotification.metadata, count: 1 },
    });
  };

  // Helper function to create a notification with type-specific defaults
  const createNotification = async (
    type: NotificationType,
    content: string | NotificationContent,
    options?: {
      title?: string;
      duration?: number | null;
      dismissible?: boolean;
      actions?: NotificationAction[];
      metadata?: Record<string, any>;
      priority?: NotificationPriority;
      icon?: string | React.ReactNode;
      groupSimilar?: boolean;
      category?: string;
      useQueue?: boolean;
      saveToServer?: boolean;
      expiresAt?: Date;
    }
  ) => {
    // Convert string content to NotificationContent object
    const notificationContent: NotificationContent =
      typeof content === "string" ? { text: content } : content;

    // Set default duration based on notification type
    const duration =
      options?.duration !== undefined
        ? options.duration
        : DEFAULT_DURATIONS[type];

    // Prepare metadata with category if provided
    const metadata = {
      ...options?.metadata,
      ...(options?.category ? { category: options.category } : {}),
      ...(options?.expiresAt ? { expiresAt: options.expiresAt } : {}),
    };

    const notification = {
      type,
      content: notificationContent,
      duration,
      dismissible: options?.dismissible ?? true,
      title: options?.title,
      actions: options?.actions,
      metadata,
      priority: options?.priority ?? "medium",
      icon: options?.icon,
    };

    // Handle grouping if enabled
    if (options?.groupSimilar) {
      return groupSimilarNotifications(notification);
    }

    // Handle queuing if enabled
    if (options?.useQueue) {
      return queueOrShowNotification(notification);
    }

    // Default behavior - with optional server persistence
    return addNotification(notification, options?.saveToServer || false);
  };

  // Track notification interactions for analytics
  const trackNotificationInteraction = (
    notificationId: string,
    action: "view" | "dismiss" | "click" | "action"
  ) => {
    // Log to analytics service if available
    if (typeof window !== "undefined" && (window as any).analyticsService) {
      (window as any).analyticsService.trackEvent("notification_interaction", {
        notificationId,
        action,
        timestamp: Date.now(),
      });
    } else {
      console.log("Notification interaction:", {
        notificationId,
        action,
        timestamp: Date.now(),
      });
    }
  };

  // Type-specific notification creators
  const success = (content: string | NotificationContent, options?: any) =>
    createNotification("success", content, options);

  const error = (content: string | NotificationContent, options?: any) =>
    createNotification("error", content, options);

  const info = (content: string | NotificationContent, options?: any) =>
    createNotification("info", content, options);

  const warning = (content: string | NotificationContent, options?: any) =>
    createNotification("warning", content, options);

  const custom = (content: string | NotificationContent, options?: any) =>
    createNotification("custom", content, options);

  // Server-specific notification creators that automatically save to server
  const serverSuccess = (
    content: string | NotificationContent,
    options?: any
  ) =>
    createNotification("success", content, { ...options, saveToServer: true });

  const serverError = (content: string | NotificationContent, options?: any) =>
    createNotification("error", content, { ...options, saveToServer: true });

  const serverInfo = (content: string | NotificationContent, options?: any) =>
    createNotification("info", content, { ...options, saveToServer: true });

  const serverWarning = (
    content: string | NotificationContent,
    options?: any
  ) =>
    createNotification("warning", content, { ...options, saveToServer: true });

  const serverCustom = (content: string | NotificationContent, options?: any) =>
    createNotification("custom", content, { ...options, saveToServer: true });

  return {
    notify: createNotification,
    success,
    error,
    info,
    warning,
    custom,
    // Server variants
    serverSuccess,
    serverError,
    serverInfo,
    serverWarning,
    serverCustom,
    // Other methods
    remove: removeNotification,
    update: updateNotification,
    clearAll: clearAllNotifications,
    markAsRead,
    trackInteraction: trackNotificationInteraction,
  };
};
