import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { Search, HelpCircle, X } from 'lucide-react';

// In the documentation, we'll simulate the SearchableTable rather than using the actual component
// This is because the component requires complex state and callbacks that are difficult to mock in a documentation context
const SearchableTableDoc: React.FC = () => {
  // Sample data to display in our table example
  const users = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Editor', status: 'Inactive' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Viewer', status: 'Active' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'Editor', status: 'Active' },
    { id: 5, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active' },
  ];

  const columns = [
    { key: 'name', label: 'Name' },
    { key: 'email', label: 'Email' },
    { key: 'role', label: 'Role' },
    { key: 'status', label: 'Status' },
  ];

  // Render a simulated version of the SearchableTable
  const SimulatedSearchableTable = () => (
    <div className="space-y-4">
      <div className="relative flex-1 max-w-md">
        <div className="relative">
          <input
            type="text"
            placeholder='Search data... (Press "/" for help)'
            className="w-full pl-10 pr-20 py-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-full text-sm"
          />
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          <div className="absolute right-3 top-2 text-xs text-gray-400 flex items-center space-x-2">
            <button
              className="hover:text-gray-600"
            >
              <HelpCircle className="h-4 w-4" />
            </button>
            <span>⌘K</span>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mt-3">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead>
            <tr>
              {columns.map((column) => (
                <th 
                  key={column.key} 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
            {users.map((item) => (
              <tr key={item.id}>
                {columns.map((column) => (
                  <td 
                    key={`${item.id}-${column.key}`} 
                    className="px-6 py-4 whitespace-nowrap text-sm"
                  >
                    {String(item[column.key as keyof typeof item])}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <ComponentDoc
      title="Searchable Table"
      description="A powerful table component with advanced search capabilities, including column-specific filtering, comparison operators, and keyboard shortcuts. Ideal for data-heavy interfaces where users need to quickly find specific information."
      importCode={`import SearchableTable, { SearchQuery } from '../components/search/SearchableTable';`}
      usage={
        <div className="w-full">
          <SimulatedSearchableTable />
        </div>
      }
      code={`import { useState } from 'react';
import SearchableTable, { SearchQuery } from '../components/search/SearchableTable';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  status: string;
}

function UserTable() {
  const [users, setUsers] = useState<User[]>([/* user data */]);
  const [filteredData, setFilteredData] = useState<User[]>(users);
  const [page, setPage] = useState(0);
  const [limit] = useState(10);

  const columns = [
    { key: 'name', label: 'Name', searchable: true },
    { key: 'email', label: 'Email', searchable: true },
    { key: 'role', label: 'Role', searchable: true },
    { key: 'status', label: 'Status', searchable: true },
  ];

  const handleSearch = async (query: SearchQuery) => {
    // In a real app, call your API with the search query
    // For example: const response = await api.searchUsers(query);
    // setFilteredData(response.data);
    
    // For demo purposes, filter locally:
    let filtered = [...users];
    
    if (query.filters.length > 0) {
      filtered = users.filter(user => {
        // Apply filters...
      });
    }
    
    setFilteredData(filtered);
    setPage(query.page);
  };

  return (
    <SearchableTable
      data={filteredData}
      columns={columns}
      onSearch={handleSearch}
      page={page}
      limit={limit}
      totalCount={filteredData.length}
      onPageChange={setPage}
      renderTable={(data) => (
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              {columns.map((column) => (
                <th key={column.key}>{column.label}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((item) => (
              <tr key={item.id}>
                {columns.map((column) => (
                  <td key={\`\${item.id}-\${column.key}\`}>
                    {item[column.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      )}
    />
  );
}`}
      props={[
        {
          name: 'data',
          type: 'T[]',
          description: 'Array of data items to display in the table.',
          required: true
        },
        {
          name: 'columns',
          type: '{ key: keyof T; label: string; searchable?: boolean; format?: (value: any) => string; operators?: SearchFilter["operator"][]; }[]',
          description: 'Array of column definitions that specify how to display and search data.',
          required: true
        },
        {
          name: 'onSearch',
          type: '(query: SearchQuery) => Promise<void>',
          description: 'Callback function called when the search query changes. Receives the parsed search query object.',
          required: true
        },
        {
          name: 'loading',
          type: 'boolean',
          description: 'Whether the data is currently loading.',
          required: false
        },
        {
          name: 'page',
          type: 'number',
          description: 'Current page number (0-indexed).',
          required: true
        },
        {
          name: 'limit',
          type: 'number',
          description: 'Number of items per page.',
          required: true
        },
        {
          name: 'totalCount',
          type: 'number',
          description: 'Total number of items across all pages.',
          required: false
        },
        {
          name: 'onPageChange',
          type: '(page: number) => void',
          description: 'Callback function called when the page changes.',
          required: true
        },
        {
          name: 'renderTable',
          type: '(data: T[]) => React.ReactNode',
          description: 'Render prop that receives the filtered data and should return the table UI.',
          required: true
        }
      ]}
      variants={[
        {
          title: 'Basic Search Table',
          description: 'Standard implementation with the core search functionality.',
          component: (
            <div className="w-full">
              <SimulatedSearchableTable />
            </div>
          ),
          code: `<SearchableTable
  data={filteredData}
  columns={columns}
  onSearch={handleSearch}
  page={page}
  limit={limit}
  totalCount={filteredData.length}
  onPageChange={setPage}
  renderTable={(data) => (
    <table className="min-w-full divide-y divide-gray-200">
      <thead>
        <tr>
          {columns.map((column) => (
            <th key={column.key}>{column.label}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((item) => (
          <tr key={item.id}>
            {columns.map((column) => (
              <td key={\`\${item.id}-\${column.key}\`}>
                {item[column.key]}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  )}
/>`
        },
        {
          title: 'With Search Syntax Hint',
          description: 'Shows the search syntax help tooltip for advanced filtering options.',
          component: (
            <div className="w-full">
              <div className="space-y-4">
                <div className="relative flex-1 max-w-md">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder='Search data... (Press "/" for help)'
                      className="w-full pl-10 pr-20 py-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-full text-sm"
                    />
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <div className="absolute right-3 top-2 text-xs text-gray-400 flex items-center space-x-2">
                      <button className="hover:text-gray-600">
                        <HelpCircle className="h-4 w-4" />
                      </button>
                      <span>⌘K</span>
                    </div>
                  </div>
                  
                  {/* Syntax help tooltip */}
                  <div className="absolute z-50 mt-2 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded shadow-lg p-4">
                    <h3 className="font-medium text-sm mb-2">Search Syntax</h3>
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                      <div>
                        <code className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">""</code>
                        <span className="ml-2">Exact phrase match</span>
                      </div>
                      <div>
                        <code className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">column:value</code>
                        <span className="ml-2">Search in specific column</span>
                      </div>
                      <div>
                        <code className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">{'column>10'}</code>
                        <span className="ml-2">Numeric comparison (&gt;, &gt;=, &lt;, &lt;=)</span>
                      </div>
                      <div>
                        <code className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">-term</code>
                        <span className="ml-2">Exclude term</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="overflow-x-auto mt-3">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                      <tr>
                        {columns.map((column) => (
                          <th 
                            key={column.key} 
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            {column.label}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {users.slice(0, 3).map((item) => (
                        <tr key={item.id}>
                          {columns.map((column) => (
                            <td 
                              key={`${item.id}-${column.key}`} 
                              className="px-6 py-4 whitespace-nowrap text-sm"
                            >
                              {String(item[column.key as keyof typeof item])}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          ),
          code: `// The search syntax help tooltip appears when the help button is clicked
<SearchableTable
  // ... props
  renderTable={(data) => (
    // ... table rendering
  )}
/>`
        }
      ]}
      bestPractices={{
        do: [
          'Provide clear search syntax instructions through the help tooltip.',
          'Include keyboard shortcuts (⌘K) for quick access to the search input.',
          'Use debounced search to prevent excessive API calls while typing.',
          'Define searchable columns explicitly to optimize performance.',
          'Implement proper error handling for search operations.',
          'Support advanced search operators where appropriate for power users.'
        ],
        dont: [
          'Don\'t apply search to all columns if some contain large text or binary data.',
          'Don\'t implement complex search syntax without providing clear documentation.',
          'Don\'t forget to reset to the first page when applying a new search.',
          'Don\'t make the search input too narrow - users need to see what they\'re typing.',
          'Don\'t set the debounce delay too short or too long (300-500ms is usually appropriate).'
        ]
      }}
      accessibility={[
        'The search input is keyboard accessible with focus management.',
        'Clear button for the search input helps users quickly reset their search.',
        'Keyboard shortcuts have visual indicators (⌘K shown in the UI).',
        'Help tooltip provides clear instructions for advanced search syntax.',
        'Search input has proper aria labels and placeholder text.',
        'Color contrast meets WCAG standards for both light and dark themes.'
      ]}
    />
  );
};

export default SearchableTableDoc;