import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { Layout, Sidebar, Menu } from 'lucide-react';

const LayoutDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Layout"
      description="The main application layout component that provides a responsive structure with sidebar navigation and content area."
      importCode={`import { Layout } from '../components/layouts/Layout';`}
      usage={
        <div className="w-full bg-gray-50 dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="w-full h-64 bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 flex">
            {/* Simulated Sidebar */}
            <div className="hidden sm:block w-64 bg-gray-100 dark:bg-gray-850 border-r border-gray-200 dark:border-gray-700 p-4">
              <div className="h-8 w-24 bg-blue-100 dark:bg-blue-900/30 rounded mb-4"></div>
              <div className="space-y-2">
                <div className="h-6 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-6 w-full bg-blue-500 dark:bg-blue-600 rounded"></div>
                <div className="h-6 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-6 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            
            {/* Mobile Header */}
            <div className="sm:hidden absolute top-0 left-0 right-0 h-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-2">
              <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
            
            {/* Content Area */}
            <div className="flex-1 p-4 pt-12 sm:pt-4">
              <div className="h-full flex flex-col">
                <div className="h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="flex-1 bg-gray-50 dark:bg-gray-750 rounded p-2">
                  Content Area (Outlet)
                </div>
              </div>
            </div>
          </div>
        </div>
      }
      code={`// Usage in Router configuration
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Layout } from '../components/layouts/Layout';
import Dashboard from '../pages/Dashboard';
import Settings from '../pages/Settings';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route element={<Layout />}>
          <Route path="/" element={<Dashboard />} />
          <Route path="/settings" element={<Settings />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}`}
      props={[
        {
          name: 'children',
          type: 'React.ReactNode',
          description: 'Content rendered inside the layout. Generally not used directly since this component uses React Router\'s Outlet.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Responsive Layout',
          description: 'The layout adapts to different screen sizes, showing a fixed sidebar on larger screens and a toggleable drawer on mobile devices.',
          component: (
            <div className="space-y-4">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <h3 className="text-sm font-medium mb-2">Desktop Layout</h3>
                <div className="h-40 bg-gray-50 dark:bg-gray-750 rounded-lg border border-gray-200 dark:border-gray-700 flex overflow-hidden">
                  <div className="w-1/4 bg-gray-100 dark:bg-gray-850 border-r border-gray-200 dark:border-gray-700 p-2">
                    <div className="h-6 w-20 bg-blue-100 dark:bg-blue-900/30 rounded mb-2"></div>
                    <div className="space-y-1">
                      <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                      <div className="h-4 w-full bg-blue-500 dark:bg-blue-600 rounded"></div>
                      <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                  </div>
                  <div className="flex-1 p-2">
                    <div className="h-full bg-white dark:bg-gray-800 rounded"></div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <h3 className="text-sm font-medium mb-2">Mobile Layout</h3>
                <div className="h-40 bg-gray-50 dark:bg-gray-750 rounded-lg border border-gray-200 dark:border-gray-700 relative overflow-hidden">
                  {/* Mobile Header */}
                  <div className="h-8 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-2">
                    <div className="h-5 w-5 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                  
                  {/* Sidebar Drawer (shown as partially open) */}
                  <div className="absolute top-0 left-0 h-full w-3/4 bg-gray-100 dark:bg-gray-850 border-r border-gray-200 dark:border-gray-700 p-2 transform -translate-x-2/3">
                    <div className="h-6 w-20 bg-blue-100 dark:bg-blue-900/30 rounded mb-2"></div>
                    <div className="space-y-1">
                      <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                      <div className="h-4 w-full bg-blue-500 dark:bg-blue-600 rounded"></div>
                      <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                  </div>
                  
                  {/* Content */}
                  <div className="h-full pt-8 p-2">
                    <div className="h-full bg-white dark:bg-gray-800 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          ),
          code: `// The Layout component handles responsive behavior automatically
// It shows a toggle button on mobile and fixed sidebar on desktop

<Layout>
  {/* Your routes/content go here */}
  <Outlet />
</Layout>`
        },
        {
          title: 'Authentication Integration',
          description: 'The layout checks for authenticated users and redirects to the login page if not authenticated.',
          component: (
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="prose dark:prose-invert prose-sm max-w-none">
                <p>The Layout component includes authentication checking:</p>
                <ul>
                  <li>Shows a loading spinner while authentication state is being checked</li>
                  <li>Redirects to login page if the user is not authenticated</li>
                  <li>Renders the main layout with sidebar and outlet if authenticated</li>
                </ul>
                <pre><code>{`// Inside Layout.tsx
const { user, isLoading } = useAuth();

if (isLoading) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
    </div>
  );
}

if (!user) {
  return <Navigate to="/login" state={{ from: location }} replace />;
}`}</code></pre>
              </div>
            </div>
          ),
          code: `// Authentication is handled internally by the Layout component
// It will automatically redirect unauthenticated users to the login page

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route element={<Layout />}>
          {/* Protected routes */}
          <Route path="/" element={<Dashboard />} />
          <Route path="/settings" element={<Settings />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}`
        }
      ]}
      bestPractices={{
        do: [
          'Use as the main wrapper for authenticated application pages.',
          'Maintain the responsive design by working within the layout\'s structure.',
          'Place route-specific content inside the routes, not in the Layout component.',
          'Use appropriate breakpoints for responsive behavior (the layout uses lg breakpoint for sidebar).',
          'Consider performance implications when adding components to every page via the layout.'
        ],
        dont: [
          'Don\'t modify the layout structure unless absolutely necessary.',
          'Don\'t add page-specific content directly to the Layout component.',
          'Don\'t override the authentication flow without understanding the implications.',
          'Don\'t remove the mobile navigation controls as they\'re essential for smaller screens.',
          'Don\'t nest Layout components inside each other.'
        ]
      }}
      accessibility={[
        'The sidebar navigation provides a consistent and accessible navigation experience.',
        'Mobile navigation toggles are properly labeled for screen readers.',
        'The layout maintains a logical tab order for keyboard navigation.',
        'Contents rendered through the Outlet inherit the layout\'s semantic structure.',
        'The layout respects color contrast standards for light and dark themes.',
        'Loading states provide appropriate visual feedback for all users.'
      ]}
    />
  );
};

export default LayoutDoc;