import React from 'react';

const IntroductionDoc: React.FC = () => {
  return (
    <div className="max-w-4xl">
      <h1 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
        Resultid Design System
      </h1>
      
      <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
        Welcome to the Resultid Design System, a comprehensive guide to our UI components,
        design patterns, and best practices. This design system helps ensure consistency and
        quality across all Resultid products.
      </p>
      
      <h2 className="text-2xl font-semibold mt-8 mb-4 text-gray-900 dark:text-white">
        Purpose
      </h2>
      
      <p className="mb-4 text-gray-700 dark:text-gray-300">
        This design system serves several key purposes:
      </p>
      
      <ul className="list-disc pl-6 mb-6 space-y-2 text-gray-700 dark:text-gray-300">
        <li>
          <strong>Consistency:</strong> Maintain visual and functional consistency across all Resultid applications.
        </li>
        <li>
          <strong>Efficiency:</strong> Speed up development by providing reusable components with clear documentation.
        </li>
        <li>
          <strong>Collaboration:</strong> Improve collaboration between designers and developers with a shared language.
        </li>
        <li>
          <strong>Quality:</strong> Ensure components meet accessibility standards and best practices.
        </li>
        <li>
          <strong>Evolution:</strong> Allow the design system to grow and evolve with the product.
        </li>
      </ul>
      
      <h2 className="text-2xl font-semibold mt-8 mb-4 text-gray-900 dark:text-white">
        How to Use This Guide
      </h2>
      
      <p className="mb-4 text-gray-700 dark:text-gray-300">
        The sidebar on the left provides navigation through different categories of components and guidelines.
        Each component page includes:
      </p>
      
      <ul className="list-disc pl-6 mb-6 space-y-2 text-gray-700 dark:text-gray-300">
        <li>An overview of the component's purpose and usage</li>
        <li>Interactive examples showing the component in action</li>
        <li>Code snippets for implementation</li>
        <li>Available props and their descriptions</li>
        <li>Variants and customization options</li>
        <li>Best practices for when and how to use the component</li>
        <li>Accessibility considerations</li>
      </ul>
      
      <h2 className="text-2xl font-semibold mt-8 mb-4 text-gray-900 dark:text-white">
        Design Principles
      </h2>
      
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Clarity</h3>
          <p className="text-gray-700 dark:text-gray-300">
            Interfaces should be intuitive and eliminate ambiguity. Users should always understand 
            what actions they can take and what the outcomes will be.
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Efficiency</h3>
          <p className="text-gray-700 dark:text-gray-300">
            Focus on helping users complete their tasks with minimal effort. Streamline workflows 
            and reduce cognitive load whenever possible.
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Consistency</h3>
          <p className="text-gray-700 dark:text-gray-300">
            User interfaces should be predictable and familiar across the platform. Common patterns 
            help users learn the system quickly.
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Accessibility</h3>
          <p className="text-gray-700 dark:text-gray-300">
            Design for users of all abilities, ensuring interfaces are perceivable, operable, 
            understandable, and robust for everyone.
          </p>
        </div>
      </div>
      
      <h2 className="text-2xl font-semibold mt-8 mb-4 text-gray-900 dark:text-white">
        Getting Started
      </h2>
      
      <p className="mb-4 text-gray-700 dark:text-gray-300">
        To get started with the design system, browse the categories in the sidebar:
      </p>
      
      <ul className="list-disc pl-6 mb-6 space-y-2 text-gray-700 dark:text-gray-300">
        <li>Begin with the <strong>Color System</strong> and <strong>Typography</strong> to understand our fundamental design tokens</li>
        <li>Explore <strong>Core UI Components</strong> to see the building blocks of our interfaces</li>
        <li>Check out more complex components in other categories as needed for your project</li>
      </ul>
      
      <div className="bg-blue-50 dark:bg-blue-900 border-l-4 border-blue-500 dark:border-blue-400 p-4 rounded mt-8">
        <p className="text-blue-800 dark:text-blue-200">
          This design system is a living document and will continue to evolve as our product grows.
          If you have questions or suggestions, please reach out to the design team.
        </p>
      </div>
    </div>
  );
};

export default IntroductionDoc;