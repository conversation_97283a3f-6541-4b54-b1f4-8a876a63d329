import React, { useState } from 'react';
import ComponentDoc from '../ComponentDoc';
import { TagInput } from '../../FileLibrary/FileRow/TagInput';

const TagInputDoc: React.FC = () => {
  const [tags, setTags] = useState<string[]>(['react', 'typescript']);
  const [showTagInput, setShowTagInput] = useState(true);

  const handleTagChange = (newTags: string[]) => {
    setTags(newTags);
  };

  const handleClose = () => {
    // In a real implementation, this would close the tag input
    // For the documentation, we'll keep it visible
    console.log('Close tag input');
  };

  return (
    <ComponentDoc
      title="Tag Input"
      description="A flexible component for managing tags or labels with keyboard navigation, suggestions, and inline editing capabilities. Perfect for categorizing content, filtering, or metadata management."
      importCode={`import { TagInput } from '../components/FileLibrary/FileRow/TagInput';`}
      usage={
        <div className="min-w-[300px]">
          {showTagInput && (
            <TagInput 
              tags={tags} 
              onChange={handleTagChange} 
              onClose={handleClose} 
            />
          )}
        </div>
      }
      code={`const [tags, setTags] = useState<string[]>(['react', 'typescript']);

<TagInput 
  tags={tags} 
  onChange={setTags} 
  onClose={() => setShowTagInput(false)} 
/>`}
      props={[
        {
          name: 'tags',
          type: 'string[]',
          description: 'Array of tag strings currently applied.',
          required: true
        },
        {
          name: 'onChange',
          type: '(tags: string[]) => void',
          description: 'Callback function that receives the updated tags array when tags are added or removed.',
          required: true
        },
        {
          name: 'onClose',
          type: '() => void',
          description: 'Callback function that is called when the user is done with tag editing or presses Escape.',
          required: true
        }
      ]}
      variants={[
        {
          title: 'Empty Tags',
          description: 'TagInput with no initial tags.',
          component: (
            <div className="min-w-[300px]">
              <TagInput 
                tags={[]} 
                onChange={() => {}} 
                onClose={() => {}} 
              />
            </div>
          ),
          code: `const [tags, setTags] = useState<string[]>([]);

<TagInput 
  tags={tags} 
  onChange={setTags} 
  onClose={handleClose} 
/>`
        },
        {
          title: 'Multiple Tags',
          description: 'TagInput with multiple pre-existing tags.',
          component: (
            <div className="min-w-[300px]">
              <TagInput 
                tags={['react', 'typescript', 'tailwind', 'nextjs', 'design-system']} 
                onChange={() => {}} 
                onClose={() => {}} 
              />
            </div>
          ),
          code: `const [tags, setTags] = useState<string[]>([
  'react', 'typescript', 'tailwind', 'nextjs', 'design-system'
]);

<TagInput 
  tags={tags} 
  onChange={setTags} 
  onClose={handleClose} 
/>`
        }
      ]}
      bestPractices={{
        do: [
          'Use for categorization, filtering, or organizing content with multiple labels.',
          'Provide clear instructions on how to add tags (e.g., "Press Enter to add").',
          'Consider offering tag suggestions based on previously used tags.',
          'Ensure proper contrast between tag background and text for readability.',
          'Maintain consistency in tag styling throughout your application.'
        ],
        dont: [
          'Don\'t use for single-selection scenarios; consider a dropdown or radio buttons instead.',
          'Don\'t allow excessively long tag texts that might break your layout.',
          'Don\'t overcomplicate the UI with too many options or complex interactions.',
          'Don\'t place in areas where horizontal space is very limited.',
          'Don\'t allow duplicate tags to be added to the same item.'
        ]
      }}
      accessibility={[
        'Supports keyboard navigation for adding tags (Enter) and removing tags (Backspace).',
        'Provides clear visual indication of interactive elements like the tag removal button.',
        'Focuses the input field automatically when the component mounts for immediate interaction.',
        'Escape key provides a way to exit the tagging interface without making changes.',
        'Tag removal buttons are properly sized for touch targets.',
        'Visual feedback is provided for different tag states (selected, available, disabled).'
      ]}
    />
  );
};

export default TagInputDoc;