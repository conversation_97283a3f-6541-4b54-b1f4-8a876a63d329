import React from 'react';
import ComponentDoc from '../ComponentDoc';

const NumberFormatterDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Number Formatter"
      description="A utility for formatting numbers into human-readable strings with support for compact notation, suffixes, localization, and special formatting for percentages and bytes."
      
      importCode={`import { formatNumber, formatPercentage, formatBytes } from '../utils/numberFormatter';`}
      
      code={`// Format a number with default settings (compact notation with 1 decimal place)
formatNumber(1234); // "1.2K"

// Format with 2 decimal places
formatNumber(1234, { decimals: 2 }); // "1.23K"

// Format without compact notation
formatNumber(1234, { compact: false }); // "1,234"

// Format with forced decimals
formatNumber(1200, { forceDecimals: true }); // "1.2K"

// Format a percentage
formatPercentage(0.1234); // "12.3%"

// Format bytes
formatBytes(1024); // "1.0 KB"`}
      
      props={[
        {
          name: 'formatNumber',
          type: `(value: number | null | undefined, options?: {
  decimals?: number;
  forceDecimals?: boolean;
  suffixes?: Record<string, number>;
  compact?: boolean;
  locale?: string;
}) => string`,
          description: 'Formats a number with appropriate suffix (K, M, B) and decimal places.',
          required: false
        },
        {
          name: 'formatPercentage',
          type: `(value: number | null | undefined, options?: {
  decimals?: number;
  forceDecimals?: boolean;
  locale?: string;
}) => string`,
          description: 'Formats a number as a percentage (e.g., 0.1 becomes "10%").',
          required: false
        },
        {
          name: 'formatBytes',
          type: `(bytes: number | null | undefined, options?: {
  decimals?: number;
  forceDecimals?: boolean;
  compact?: boolean;
  locale?: string;
}) => string`,
          description: 'Formats bytes into human-readable string with appropriate units (B, KB, MB, GB, etc.).',
          required: false
        }
      ]}
      
      variants={[
        {
          title: 'Basic Number Formatting',
          description: 'Examples of basic number formatting with different options.',
          component: (
            <div className="space-y-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <table className="w-full text-left">
                <thead>
                  <tr>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Input</th>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Options</th>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Output</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1234</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">default</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.2K</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1234</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">{` decimals: 2 `}</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.23K</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1234</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">{` compact: false `}</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1,234</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1200</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">{` forceDecimals: true `}</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.2K</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1200000</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">default</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.2M</td>
                  </tr>
                </tbody>
              </table>
            </div>
          ),
          code: `// Default formatting
formatNumber(1234); // "1.2K"

// With 2 decimal places
formatNumber(1234, { decimals: 2 }); // "1.23K"

// Without compact notation
formatNumber(1234, { compact: false }); // "1,234"

// With forced decimals
formatNumber(1200, { forceDecimals: true }); // "1.2K"

// Large number
formatNumber(1200000); // "1.2M"`
        },
        {
          title: 'Percentage Formatting',
          description: 'Examples of formatting numbers as percentages.',
          component: (
            <div className="space-y-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <table className="w-full text-left">
                <thead>
                  <tr>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Input</th>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Options</th>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Output</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">0.1234</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">default</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">12.3%</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">0.1234</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">{` decimals: 2 `}</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">12.34%</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">0.5</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">default</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">50%</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">0.5</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">{` forceDecimals: true `}</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">50.0%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          ),
          code: `// Default formatting
formatPercentage(0.1234); // "12.3%"

// With 2 decimal places
formatPercentage(0.1234, { decimals: 2 }); // "12.34%"

// Whole number percentage
formatPercentage(0.5); // "50%"

// With forced decimals
formatPercentage(0.5, { forceDecimals: true }); // "50.0%"`
        },
        {
          title: 'Byte Formatting',
          description: 'Examples of formatting byte values.',
          component: (
            <div className="space-y-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <table className="w-full text-left">
                <thead>
                  <tr>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Input</th>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Options</th>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Output</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1024</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">default</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.0 KB</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1536</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">default</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.5 KB</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1048576</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">default</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.0 MB</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">1073741824</td>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">{` decimals: 2 `}</td>
                    <td className="py-2 text-sm font-medium text-gray-900 dark:text-white">1.00 GB</td>
                  </tr>
                </tbody>
              </table>
            </div>
          ),
          code: `// Format kilobytes
formatBytes(1024); // "1.0 KB"

// Format partial kilobytes
formatBytes(1536); // "1.5 KB"

// Format megabytes
formatBytes(1048576); // "1.0 MB"

// Format gigabytes with 2 decimal places
formatBytes(1073741824, { decimals: 2 }); // "1.00 GB"`
        }
      ]}
      
      bestPractices={{
        do: [
          'Use the appropriate formatter for the type of data (formatNumber for general numbers, formatPercentage for percentages, formatBytes for file sizes)',
          'Keep formatting consistent throughout the application',
          'Adjust decimal places based on the precision needed for the specific context',
          'Use compact notation for large numbers when space is limited',
          'Consider using locale-specific formatting for international applications'
        ],
        dont: [
          'Don\'t use too many decimal places for values where precision isn\'t important',
          'Don\'t mix formatting styles for the same type of data in different parts of the UI',
          'Don\'t use formatPercentage for values that aren\'t actually percentages',
          'Don\'t forget to handle null/undefined values (though the functions handle this gracefully)',
          'Don\'t use complicated custom suffixes that may confuse users'
        ]
      }}
      
      accessibility={[
        'Formatted numbers maintain their semantic meaning',
        'Compact notation helps reduce cognitive load for large numbers',
        'Units (K, M, B, KB, MB, %) provide context for the values',
        'Consistent formatting helps users understand data relationships',
        'Use appropriate number of decimal places to maintain readability'
      ]}
    />
  );
};

export default NumberFormatterDoc;