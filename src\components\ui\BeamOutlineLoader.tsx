import React, { useState, useEffect } from 'react';
import clsx from 'clsx';

interface BeamOutlineLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function BeamOutlineLoader({ size = 'md', className }: BeamOutlineLoaderProps) {
  // State to track dark mode
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  // Effect to detect dark mode and observe changes
  useEffect(() => {
    // Function to check dark mode status
    const checkDarkMode = () => {
      const isDark = document.documentElement.classList.contains('dark');
      setIsDarkMode(isDark);
    };
    
    // Initialize on mount
    checkDarkMode();
    
    // Set up observer to watch for class changes on html element
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });
    
    // Clean up
    return () => observer.disconnect();
  }, []);

  // Size classes mapping with additional padding to prevent cut-off
  const sizeMap = {
    sm: 'h-12 w-12', // Increased from h-10 w-10
    md: 'h-20 w-20', // Increased from h-16 w-16
    lg: 'h-28 w-28'  // Increased from h-24 w-24
  };

  // SVG paths from the logo
  const pinkShapePath = "M26.9776 81.6754C27.9902 83.5898 30.3549 84.3252 32.2594 83.3179L53.915 71.8641C55.8195 70.8568 56.5425 68.4883 55.53 66.5739L43.6496 44.1116C43.1433 43.1544 43.5048 41.9702 44.457 41.4665L66.8023 29.648C68.7068 28.6407 69.4299 26.2721 68.4173 24.3577L56.9036 2.58874C55.891 0.674304 53.5263 -0.0610889 51.6218 0.946196L2.44836 26.9543C1.02001 27.7098 0.256217 29.231 0.390762 30.752C0.435611 31.259 0.580268 31.766 0.833407 32.2446L26.9776 81.6754Z";
  const blueShapePath = "M97.1086 2.09387C96.0961 0.179427 93.7314 -0.555959 91.8269 0.451327L70.1713 11.9051C68.2668 12.9124 67.5438 15.2809 68.5563 17.1954L80.4367 39.6576C80.943 40.6148 80.5815 41.7991 79.6292 42.3027L57.2839 54.1213C55.3795 55.1286 54.6564 57.4971 55.669 59.4115L67.1827 81.1805C68.1953 83.0949 70.56 83.8303 72.4645 82.823L121.638 56.8149C123.066 56.0594 123.83 54.5383 123.696 53.0173C123.651 52.5102 123.506 52.0033 123.253 51.5247L97.1086 2.09387Z";
  
  return (
    <div className={clsx(
      'relative flex items-center justify-center',
      sizeMap[size],
      className
    )}>
      {/* Light mode version - ONLY SIMPLE SHAPES AND STROKES */}
      {!isDarkMode && (
        <svg 
          viewBox="0 0 140 100" // Increased viewBox to add padding around the shapes
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full"
          aria-label="Loading"
          role="img"
          style={{ padding: '8px' }} // Add padding to prevent any cut-off
        >
          <g transform="translate(8, 8)"> {/* Center the paths in the expanded viewBox */}
            {/* Very faint background shapes */}
            <path 
              d={pinkShapePath} 
              fill="#FA0369" 
              opacity="0.07"
            />
            <path 
              d={blueShapePath} 
              fill="#2663FF" 
              opacity="0.07"
            />
            
            {/* Simple Clean Strokes with NO Effects - Now with rounded ends */}
            <path 
              d={pinkShapePath} 
              stroke="#FA0369"
              strokeWidth="6"
              strokeLinecap="round" // Added rounded ends
              strokeLinejoin="round" // Added rounded corners
              fill="none"
              style={{ 
                strokeDasharray: "70 320",
                animation: "cleanBeamPink 3s linear infinite"
              }}
            />
            
            <path 
              d={blueShapePath} 
              stroke="#2663FF"
              strokeWidth="6"
              strokeLinecap="round" // Added rounded ends
              strokeLinejoin="round" // Added rounded corners
              fill="none"
              style={{ 
                strokeDasharray: "70 320",
                animation: "cleanBeamBlue 3s linear infinite"
              }}
            />
          </g>
        </svg>
      )}
      
      {/* Dark mode version - WITH glow effects */}
      {isDarkMode && (
        <svg 
          viewBox="0 0 140 100" // Increased viewBox to add padding around the shapes
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full"
          aria-label="Loading"
          role="img"
          style={{ padding: '8px' }} // Add padding to prevent any cut-off
        >
          <g transform="translate(8, 8)"> {/* Center the paths in the expanded viewBox */}
            {/* Dark Background Shapes */}
            <path 
              d={pinkShapePath} 
              fill="#FA0369" 
              opacity="0.18"
            />
            <path 
              d={blueShapePath} 
              fill="#2663FF" 
              opacity="0.18"
            />
            
            {/* Dark mode Pink beam WITH glow effect */}
            <path 
              d={pinkShapePath} 
              stroke="#FF4D93"
              strokeWidth="6"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
              style={{ 
                strokeDasharray: "70 320",
                filter: "drop-shadow(0 0 8px rgba(250, 3, 105, 0.95))",
                animation: "movingBeamHighlightPink 3s linear infinite"
              }}
            />
            
            {/* Dark mode Blue beam WITH glow effect */}
            <path 
              d={blueShapePath} 
              stroke="#5B8BFF"
              strokeWidth="6"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
              style={{ 
                strokeDasharray: "70 320",
                filter: "drop-shadow(0 0 8px rgba(91, 139, 255, 0.95))",
                animation: "movingBeamHighlightBlue 3s linear infinite"
              }}
            />
          </g>
        </svg>
      )}
    </div>
  );
}