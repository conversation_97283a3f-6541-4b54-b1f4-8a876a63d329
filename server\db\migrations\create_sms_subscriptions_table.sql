-- Create SMS notification subscriptions table
CREATE TABLE IF NOT EXISTS sms_notification_subscriptions (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL,
  phone_number VARCHAR(20) NOT NULL,
  notification_types JSONB NOT NULL, -- Array of notification types
  priorities JSONB NOT NULL, -- Array of priority levels
  categories JSONB, -- Optional array of categories
  enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_sms_subscriptions_user_id ON sms_notification_subscriptions(user_id);

-- Create index for enabled subscriptions
CREATE INDEX IF NOT EXISTS idx_sms_subscriptions_enabled ON sms_notification_subscriptions(enabled) WHERE enabled = TRUE;
