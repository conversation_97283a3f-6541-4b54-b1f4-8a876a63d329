import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { History, RefreshCw, PlusCircle, MoreHorizontal, Clock, Eye } from 'lucide-react';

const HistoryPanelDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="History Panel"
      description="A component that displays and manages chat history for the AI Assistant. It allows users to view, rename, pin, and delete saved conversations, as well as start new chats and refresh the history list."
      
      importCode={`import { HistoryPanel } from '../ui/AIAssistant/HistoryPanel';`}
      
      code={`<HistoryPanel
  chatHistory={chatHistory}
  isLoadingChatHistory={isLoading}
  currentChatId={currentChat?.id || null}
  contextType="tracker"
  entityId="123"
  apiFetchAllChatHistory={fetchChatHistory}
  setChatHistory={setChatHistory}
  handleLoadChat={handleLoadChat}
  handleResetConversation={handleResetConversation}
  isEditingChatName={isEditingName}
  setIsEditingChatName={setIsEditingName}
  chatNameInput={nameInput}
  setChatNameInput={setNameInput}
  handleUpdateChatName={handleUpdateName}
  showDropdown={showDropdown}
  setShowDropdown={setShowDropdown}
  handleDeleteChat={handleDeleteChat}
  handleTogglePin={handleTogglePin}
  setActiveTab={setActiveTab}
/>`}
      
      props={[
        {
          name: 'chatHistory',
          type: 'ChatHistoryItem[]',
          description: 'Array of chat history items to display.',
          required: true
        },
        {
          name: 'isLoadingChatHistory',
          type: 'boolean',
          description: 'Whether chat history is currently being loaded.',
          required: true
        },
        {
          name: 'currentChatId',
          type: 'string | null',
          description: 'ID of the currently active chat, if any.',
          required: true
        },
        {
          name: 'contextType',
          type: 'string',
          description: 'Type of context (e.g., "tracker", "insights", "campaign", "general").',
          required: true
        },
        {
          name: 'entityId',
          type: 'string | undefined',
          description: 'ID of the entity (tracker, campaign, etc.) for history/context filtering.',
          required: false
        },
        {
          name: 'apiFetchAllChatHistory',
          type: 'Function',
          description: 'Function to fetch chat history from the API.',
          required: false
        },
        {
          name: 'setChatHistory',
          type: '(chatHistory: ChatHistoryItem[]) => void',
          description: 'Function to update the chat history state.',
          required: true
        },
        {
          name: 'handleLoadChat',
          type: '(chatId: string) => void',
          description: 'Function to load a selected chat.',
          required: true
        },
        {
          name: 'handleResetConversation',
          type: '() => void',
          description: 'Function to reset the conversation and start a new chat.',
          required: true
        },
        {
          name: 'isEditingChatName',
          type: 'string | null',
          description: 'ID of the chat being renamed, or null if not editing.',
          required: true
        },
        {
          name: 'setIsEditingChatName',
          type: '(chatId: string | null) => void',
          description: 'Function to set which chat name is being edited.',
          required: true
        },
        {
          name: 'chatNameInput',
          type: 'string',
          description: 'Current value of the chat name input field.',
          required: true
        },
        {
          name: 'setChatNameInput',
          type: '(name: string) => void',
          description: 'Function to update the chat name input value.',
          required: true
        },
        {
          name: 'handleUpdateChatName',
          type: '(chatId: string, name: string) => Promise<boolean>',
          description: 'Function to update a chat name.',
          required: true
        },
        {
          name: 'showDropdown',
          type: 'string | null',
          description: 'ID of the chat showing its dropdown menu, or null if none.',
          required: true
        },
        {
          name: 'setShowDropdown',
          type: '(chatId: string | null) => void',
          description: 'Function to control which chat dropdown is visible.',
          required: true
        },
        {
          name: 'handleDeleteChat',
          type: '(chatId: string) => Promise<boolean>',
          description: 'Function to delete a chat.',
          required: true
        },
        {
          name: 'handleTogglePin',
          type: '(chatId: string, isPinned: boolean) => Promise<boolean>',
          description: 'Function to pin/unpin a chat.',
          required: true
        },
        {
          name: 'setActiveTab',
          type: '(tab: "chat" | "context" | "history") => void',
          description: 'Function to switch between tabs in the AI Assistant.',
          required: false
        }
      ]}
      
      interfaces={[
        {
          name: 'ChatHistoryItem',
          code: `interface ChatHistoryItem {
  id: string;
  name: string;
  preview: string; // Short snippet from the conversation
  createdAt: string;
  updatedAt: string;
  messages: AIMessage[];
  contextItems?: ContextItem[];
  pinned?: boolean;
  entityType?: string;
  entityId?: string;
}`
        },
        {
          name: 'AIMessage',
          code: `interface AIMessage {
  type: 'user' | 'ai';
  content: string;
  timestamp?: string;
  usedContext?: string[]; // IDs of context items used for this message
  toolsUsed?: string[]; // Names of tools used to generate this response
  urlMetadata?: {
    url: string;
    title?: string;
    description?: string;
    ogImage?: string;
    siteName?: string;
    contentSummary?: string;
  }[];
}`
        },
        {
          name: 'ContextItem',
          code: `interface ContextItem {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'dashboard' | 'page' | 'tracker' | 'insight' | 'file';
  name: string;
  description: string;
  data?: unknown;
  icon?: React.ReactNode;
  addedAt: string;
}`
        }
      ]}
      
      variants={[
        {
          title: 'Empty State',
          description: 'Displayed when no chat history is available.',
          code: `<HistoryPanel
  chatHistory={[]}
  isLoadingChatHistory={false}
  currentChatId={null}
  contextType="general"
  setChatHistory={() => {}}
  handleLoadChat={() => {}}
  handleResetConversation={() => {}}
  isEditingChatName={null}
  setIsEditingChatName={() => {}}
  chatNameInput=""
  setChatNameInput={() => {}}
  handleUpdateChatName={async () => true}
  showDropdown={null}
  setShowDropdown={() => {}}
  handleDeleteChat={async () => true}
  handleTogglePin={async () => true}
/>`,
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-850 w-full max-w-md">
              <div className="flex items-center justify-between mb-1">
                <h3 className="text-xs font-medium text-gray-900 dark:text-white">
                  Chat History
                </h3>
                <div className="flex gap-1">
                  <button className="p-1 rounded-sm text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700" title="Refresh chat history">
                    <RefreshCw className="h-3 w-3" />
                  </button>
                  <button className="text-xs text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700" title="New chat">
                    <PlusCircle className="h-3 w-3" />
                  </button>
                </div>
              </div>
              
              <div className="flex flex-col items-center justify-center p-4 text-center text-gray-500 dark:text-gray-400">
                <History className="h-10 w-10 mb-2 text-gray-300 dark:text-gray-600" />
                <h4 className="text-xs font-medium mb-1">No Saved Chats</h4>
                <p className="text-[10px]">
                  Your conversations will appear here
                </p>
              </div>
            </div>
          )
        },
        {
          title: 'With Chat History',
          description: 'Displayed when chat history is available, showing pinned and unpinned chats.',
          code: `<HistoryPanel
  chatHistory={[
    {
      id: '1',
      name: 'Theme Analysis Discussion',
      preview: 'How do I analyze customer feedback themes?',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messages: [],
      pinned: true,
      entityType: 'general'
    },
    {
      id: '2',
      name: 'Campaign Performance',
      preview: 'What are the key metrics for my Q1 campaign?',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messages: [],
      contextItems: [{ id: 'metric1', name: 'Q1 Revenue', description: 'Revenue metrics', type: 'metric', addedAt: new Date().toISOString() }],
      entityType: 'campaign',
      entityId: '789'
    }
  ]}
  isLoadingChatHistory={false}
  currentChatId="1"
  contextType="general"
  setChatHistory={() => {}}
  handleLoadChat={() => {}}
  handleResetConversation={() => {}}
  isEditingChatName={null}
  setIsEditingChatName={() => {}}
  chatNameInput=""
  setChatNameInput={() => {}}
  handleUpdateChatName={async () => true}
  showDropdown={null}
  setShowDropdown={() => {}}
  handleDeleteChat={async () => true}
  handleTogglePin={async () => true}
  setActiveTab={() => {}}
/>`,
          component: (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-850 w-full max-w-md">
              <div className="flex items-center justify-between mb-1">
                <h3 className="text-xs font-medium text-gray-900 dark:text-white">
                  Chat History
                </h3>
                <div className="flex gap-1">
                  <button className="p-1 rounded-sm text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700" title="Refresh chat history">
                    <RefreshCw className="h-3 w-3" />
                  </button>
                  <button className="text-xs text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 flex items-center p-1 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-700" title="New chat">
                    <PlusCircle className="h-3 w-3" />
                  </button>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="mb-2">
                  <h4 className="text-[10px] uppercase tracking-wide font-medium text-gray-500 dark:text-gray-400 mb-1 px-1">
                    Pinned
                  </h4>
                  <div className="space-y-1">
                    <div className="border border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/10 rounded-sm p-2">
                      <div className="relative">
                        <div className="absolute top-1 right-1 z-20">
                          <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="More options">
                            <MoreHorizontal className="h-3 w-3" />
                          </button>
                        </div>
                        
                        <div className="flex items-center cursor-pointer mb-0.5">
                          <h4 className="text-xs font-medium text-gray-900 dark:text-white flex-1 truncate mr-2">
                            Theme Analysis Discussion
                            <span className="ml-1 text-yellow-500">•</span>
                          </h4>
                        </div>
                        
                        <div className="cursor-pointer">
                          <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1 mb-0.5">
                            How do I analyze customer feedback themes?
                          </p>
                          
                          <div className="flex items-center justify-between text-xs text-gray-400 dark:text-gray-500">
                            <div></div>
                            <div className="flex items-center gap-2">
                              <span className="flex items-center">
                                <Clock className="h-2.5 w-2.5 mr-1" />
                                <span className="text-[10px]">just now</span>
                              </span>
                              <button className="text-[10px] text-blue-500 dark:text-blue-400 hover:underline" title="View this chat">
                                View
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-[10px] uppercase tracking-wide font-medium text-gray-500 dark:text-gray-400 mb-1 px-1">
                    Other chats
                  </h4>
                  <div className="space-y-1">
                    <div className="border border-gray-200 dark:border-gray-700 rounded-sm p-2 bg-white dark:bg-gray-800">
                      <div className="relative">
                        <div className="absolute top-1 right-1 z-20">
                          <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="More options">
                            <MoreHorizontal className="h-3 w-3" />
                          </button>
                        </div>
                        
                        <div className="flex items-center cursor-pointer mb-0.5">
                          <h4 className="text-xs font-medium text-gray-900 dark:text-white flex-1 truncate mr-2">
                            Campaign Performance
                          </h4>
                        </div>
                        
                        <div className="cursor-pointer">
                          <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1 mb-0.5">
                            What are the key metrics for my Q1 campaign?
                          </p>
                          
                          <div className="flex items-center justify-between text-xs text-gray-400 dark:text-gray-500">
                            <div className="flex items-center">
                              <Eye className="h-2.5 w-2.5 mr-1" />
                              <span className="truncate text-[10px]">1</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="flex items-center">
                                <Clock className="h-2.5 w-2.5 mr-1" />
                                <span className="text-[10px]">just now</span>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        }
      ]}
      
      bestPractices={{
        do: [
          'Organize chats into logical sections (current page, pinned, other) for easy access',
          'Include relevant metadata with each chat to help users remember the conversation context',
          'Show a preview of the conversation to help users identify chats',
          'Provide clear timestamps to indicate when chats were created or updated',
          'Include a confirmation dialog before deleting chats to prevent accidental data loss',
          'Implement debouncing for actions like refreshing to prevent excessive API calls',
          'Use consistent styling to indicate the active chat'
        ],
        dont: [
          'Don\'t mix chats from different contexts without clear labeling',
          'Don\'t omit essential metadata that helps identify chat content',
          'Don\'t allow destructive actions without confirmation',
          'Don\'t refresh history data excessively and cause unnecessary API load',
          'Don\'t use ambiguous icons without tooltips or labels',
          'Don\'t make it difficult to distinguish between read and unread conversations',
          'Don\'t overcrowd the UI with too many chat history items at once'
        ]
      }}
      
      accessibility={[
        'Ensure all buttons have appropriate ARIA labels for screen readers',
        'Maintain sufficient color contrast for text and interactive elements',
        'Enable keyboard navigation for all interactive elements',
        'Provide clear focus states for interactive elements',
        'Include descriptive labels for all form inputs',
        'Ensure confirmation dialogs are accessible and can be navigated with keyboard'
      ]}
    />
  );
};

export default HistoryPanelDoc;