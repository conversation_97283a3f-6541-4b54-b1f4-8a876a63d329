/**
 * SMS Notification Subscription Routes
 */
import express from "express";
import { authenticateJWT } from "../middleware/auth.js";
import {
  getSubscription,
  createSubscription,
  updateSubscription,
  deleteSubscription,
  sendTestSMS,
} from "../controllers/smsSubscriptionController.js";

const router = express.Router();

// All routes require authentication
router.use(authenticateJWT);

// Get user's SMS subscription
router.get("/sms-subscription", getSubscription);

// Create a new SMS subscription
router.post("/sms-subscription", createSubscription);

// Update an existing SMS subscription
router.put("/sms-subscription/:id", updateSubscription);

// Delete an SMS subscription
router.delete("/sms-subscription/:id", deleteSubscription);

// Send a test SMS
router.post("/sms-subscription/test", sendTestSMS);

export default router;
