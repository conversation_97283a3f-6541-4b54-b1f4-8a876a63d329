/**
 * URL Metadata Tool 
 * Extracts and analyzes metadata from web URLs
 */

import { 
  BaseToolImplementation, 
  BaseToolResult, 
  ToolCategory, 
  ToolExecutionError 
} from './baseTool';
import * as llmApi from '../../llmApi';

/**
 * Parameters for the URL metadata tool
 */
export interface UrlMetadataToolParams {
  /** URL to extract metadata from */
  url: string;
  
  /** Optional timeout in milliseconds */
  timeout?: number;
  
  /** Whether to request a content summary */
  includeSummary?: boolean;
}

/**
 * Result of URL metadata extraction
 */
export interface UrlMetadataToolResult extends BaseToolResult {
  /** The URL that was processed */
  url: string;
  
  /** Extracted metadata if successful */
  metadata?: {
    /** Page title */
    title?: string;
    
    /** Page description or meta description */
    description?: string;
    
    /** Summary of the page content if requested */
    contentSummary?: string;
    
    /** Open Graph image URL */
    ogImage?: string;
    
    /** Site or domain name */
    siteName?: string;
    
    /** Whether a fallback was provided if metadata was incomplete */
    fallbackProvided?: boolean;
  };
}

/**
 * Tool for extracting metadata from URLs
 */
export class UrlMetadataTool extends BaseToolImplementation<UrlMetadataToolParams, UrlMetadataToolResult> {
  id = 'url-metadata';
  name = 'URL Metadata Tool';
  description = 'Extracts information from web URLs including title, description, and content summary';
  category = ToolCategory.INTEGRATION;
  requiresAuth = false;
  permissions = ['url:read'];
  
  /**
   * Validates the parameters for URL metadata extraction
   * @param params Parameters to validate
   * @returns Whether the parameters are valid
   */
  validateParams(params: UrlMetadataToolParams): boolean {
    // Check if URL is present and valid
    if (!params.url) {
      return false;
    }
    
    // Simple URL validation
    try {
      new URL(params.url);
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /**
   * Execute the URL metadata extraction
   * @param params Parameters for the operation
   * @returns Result containing the extracted metadata or error
   */
  async execute(params: UrlMetadataToolParams): Promise<UrlMetadataToolResult> {
    try {
      // Call the LLM API to fetch URL metadata
      const response = await llmApi.fetchUrlMetadata({
        url: params.url,
        timeout: params.timeout || 10000 // Default 10 second timeout
      });
      
      // Check if the request was successful
      if (!response.success) {
        return {
          success: false,
          url: params.url,
          error: {
            type: response.errorCode || 'METADATA_EXTRACTION_FAILED',
            message: response.error || 'Failed to extract metadata',
            details: response.details
          }
        };
      }
      
      // If successful, return the metadata
      return {
        success: true,
        url: params.url,
        metadata: {
          title: response.metadata.title,
          description: response.metadata.description,
          contentSummary: response.metadata.contentSummary,
          ogImage: response.metadata.ogImage,
          siteName: response.metadata.siteName,
          fallbackProvided: response.metadata.fallbackProvided
        }
      };
    } catch (error) {
      // Handle any exceptions during execution
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      return {
        success: false,
        url: params.url,
        error: {
          type: 'EXECUTION_ERROR',
          message: `Error executing URL metadata extraction: ${errorMessage}`,
          details: error
        }
      };
    }
  }
  
  /**
   * Provide examples of valid parameters for this tool
   * @returns Array of example parameters with descriptions
   */
  getExamples(): Array<{params: UrlMetadataToolParams, description: string}> {
    return [
      {
        params: { 
          url: 'https://example.com/article' 
        },
        description: 'Extract basic metadata from a web article'
      },
      {
        params: { 
          url: 'https://example.com/blog-post',
          includeSummary: true
        },
        description: 'Extract metadata with content summary from a blog post'
      },
      {
        params: { 
          url: 'https://example.com/long-page',
          timeout: 15000 
        },
        description: 'Extract metadata from a large page with extended timeout'
      }
    ];
  }
  
  /**
   * Custom help text for the URL metadata tool
   * @returns Help text string
   */
  getHelpText(): string {
    return `This tool extracts metadata from web URLs including titles, descriptions, and images. 
Use it when a user shares a URL and you want to provide context about the linked content without requiring the user to visit the page.
For example, when a user asks "What is this about?" and includes a URL, or when you need to reference information from a specific web page.`;
  }
}

// Export an instance of the tool
export default new UrlMetadataTool();