/**
 * SMS Notification Subscription Controller
 *
 * Handles creating, updating, and managing SMS notification subscriptions
 */
import { v4 as uuidv4 } from "uuid";
import { pool } from "../config/database.js";
import { validateSmsRequest } from "../utils/smsUtils.js";
import twilio from "twilio";
import { twilioConfig } from "../config/twilio.js";

// Initialize Twilio client
const twilioClient = twilio(twilioConfig.accountSid, twilioConfig.authToken);

/**
 * Get a user's SMS notification subscription
 */
export const getSubscription = async (req, res) => {
  try {
    const userId = req.user.userUUID;

    // Check if the user has a subscription
    const result = await pool.query(
      `SELECT * FROM sms_notification_subscriptions
       WHERE user_id = $1
       ORDER BY created_at DESC
       LIMIT 1`,
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No SMS subscription found for this user",
      });
    }

    // Return the subscription
    return res.status(200).json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error("Error getting SMS subscription:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to get SMS subscription",
      error: error.message,
    });
  }
};

/**
 * Create a new SMS notification subscription
 */
export const createSubscription = async (req, res) => {
  try {
    const userId = req.user.userUUID;
    const { phoneNumber, notificationTypes, priorities, categories, enabled } =
      req.body;

    // Validate phone number
    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: "Phone number is required",
      });
    }

    // Validate notification types
    if (
      !notificationTypes ||
      !Array.isArray(notificationTypes) ||
      notificationTypes.length === 0
    ) {
      return res.status(400).json({
        success: false,
        message: "At least one notification type is required",
      });
    }

    // Validate priorities
    if (!priorities || !Array.isArray(priorities) || priorities.length === 0) {
      return res.status(400).json({
        success: false,
        message: "At least one priority level is required",
      });
    }

    // Check if the user already has a subscription
    const existingResult = await pool.query(
      `SELECT * FROM sms_notification_subscriptions WHERE user_id = $1`,
      [userId]
    );

    if (existingResult.rows.length > 0) {
      // Update existing subscription
      const subscriptionId = existingResult.rows[0].id;

      const updateResult = await pool.query(
        `UPDATE sms_notification_subscriptions
         SET phone_number = $1,
             notification_types = $2,
             priorities = $3,
             categories = $4,
             enabled = $5,
             updated_at = NOW()
         WHERE id = $6
         RETURNING *`,
        [
          phoneNumber,
          JSON.stringify(notificationTypes),
          JSON.stringify(priorities),
          categories ? JSON.stringify(categories) : null,
          enabled,
          subscriptionId,
        ]
      );

      return res.status(200).json({
        success: true,
        message: "SMS subscription updated successfully",
        data: updateResult.rows[0],
      });
    }

    // Create new subscription
    const subscriptionId = uuidv4();

    const result = await pool.query(
      `INSERT INTO sms_notification_subscriptions
       (id, user_id, phone_number, notification_types, priorities, categories, enabled, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
       RETURNING *`,
      [
        subscriptionId,
        userId,
        phoneNumber,
        JSON.stringify(notificationTypes),
        JSON.stringify(priorities),
        categories ? JSON.stringify(categories) : null,
        enabled,
      ]
    );

    return res.status(201).json({
      success: true,
      message: "SMS subscription created successfully",
      data: result.rows[0],
    });
  } catch (error) {
    console.error("Error creating SMS subscription:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to create SMS subscription",
      error: error.message,
    });
  }
};

/**
 * Update an existing SMS notification subscription
 */
export const updateSubscription = async (req, res) => {
  try {
    const userId = req.user.userUUID;
    const subscriptionId = req.params.id;
    const { phoneNumber, notificationTypes, priorities, categories, enabled } =
      req.body;

    // Validate phone number
    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: "Phone number is required",
      });
    }

    // Validate notification types
    if (
      !notificationTypes ||
      !Array.isArray(notificationTypes) ||
      notificationTypes.length === 0
    ) {
      return res.status(400).json({
        success: false,
        message: "At least one notification type is required",
      });
    }

    // Validate priorities
    if (!priorities || !Array.isArray(priorities) || priorities.length === 0) {
      return res.status(400).json({
        success: false,
        message: "At least one priority level is required",
      });
    }

    // Check if the subscription exists and belongs to the user
    const existingResult = await pool.query(
      `SELECT * FROM sms_notification_subscriptions WHERE id = $1 AND user_id = $2`,
      [subscriptionId, userId]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found or does not belong to the user",
      });
    }

    // Update the subscription
    const result = await pool.query(
      `UPDATE sms_notification_subscriptions
       SET phone_number = $1,
           notification_types = $2,
           priorities = $3,
           categories = $4,
           enabled = $5,
           updated_at = NOW()
       WHERE id = $6 AND user_id = $7
       RETURNING *`,
      [
        phoneNumber,
        JSON.stringify(notificationTypes),
        JSON.stringify(priorities),
        categories ? JSON.stringify(categories) : null,
        enabled,
        subscriptionId,
        userId,
      ]
    );

    return res.status(200).json({
      success: true,
      message: "SMS subscription updated successfully",
      data: result.rows[0],
    });
  } catch (error) {
    console.error("Error updating SMS subscription:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to update SMS subscription",
      error: error.message,
    });
  }
};

/**
 * Delete an SMS notification subscription
 */
export const deleteSubscription = async (req, res) => {
  try {
    const userId = req.user.userUUID;
    const subscriptionId = req.params.id;

    // Check if the subscription exists and belongs to the user
    const existingResult = await pool.query(
      `SELECT * FROM sms_notification_subscriptions WHERE id = $1 AND user_id = $2`,
      [subscriptionId, userId]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found or does not belong to the user",
      });
    }

    // Delete the subscription
    await pool.query(
      `DELETE FROM sms_notification_subscriptions WHERE id = $1 AND user_id = $2`,
      [subscriptionId, userId]
    );

    return res.status(200).json({
      success: true,
      message: "SMS subscription deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting SMS subscription:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to delete SMS subscription",
      error: error.message,
    });
  }
};

/**
 * Send a test SMS to verify the subscription
 */
export const sendTestSMS = async (req, res) => {
  try {
    const { phoneNumber } = req.body;

    // Validate phone number
    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: "Phone number is required",
      });
    }

    // Send test SMS
    const message = await twilioClient.messages.create({
      body: "This is a test message from your notification subscription. If you received this, your SMS notifications are set up correctly!",
      messagingServiceSid: twilioConfig.defaultOptions.messagingServiceSid,
      to: phoneNumber,
    });

    return res.status(200).json({
      success: true,
      message: "Test SMS sent successfully",
      messageId: message.sid,
    });
  } catch (error) {
    console.error("Error sending test SMS:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to send test SMS",
      error: error.message,
    });
  }
};
