/**
 * Simple logger utility for consistent logging across the application
 */

// Log levels
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

// Current log level from environment or default to INFO
const currentLogLevel = LOG_LEVELS[process.env.LOG_LEVEL?.toUpperCase()] || LOG_LEVELS.INFO;

/**
 * Format a log message with timestamp and metadata
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {Object} [metadata] - Additional metadata
 * @returns {string} - Formatted log message
 */
const formatLogMessage = (level, message, metadata) => {
  const timestamp = new Date().toISOString();
  const metadataStr = metadata ? ` ${JSON.stringify(metadata)}` : '';
  return `[${timestamp}] [${level}] ${message}${metadataStr}`;
};

/**
 * Log an error message
 * @param {string} message - Error message
 * @param {Object} [metadata] - Additional metadata
 */
const error = (message, metadata) => {
  if (currentLogLevel >= LOG_LEVELS.ERROR) {
    console.error(formatLogMessage('ERROR', message, metadata));
  }
};

/**
 * Log a warning message
 * @param {string} message - Warning message
 * @param {Object} [metadata] - Additional metadata
 */
const warn = (message, metadata) => {
  if (currentLogLevel >= LOG_LEVELS.WARN) {
    console.warn(formatLogMessage('WARN', message, metadata));
  }
};

/**
 * Log an info message
 * @param {string} message - Info message
 * @param {Object} [metadata] - Additional metadata
 */
const info = (message, metadata) => {
  if (currentLogLevel >= LOG_LEVELS.INFO) {
    console.info(formatLogMessage('INFO', message, metadata));
  }
};

/**
 * Log a debug message
 * @param {string} message - Debug message
 * @param {Object} [metadata] - Additional metadata
 */
const debug = (message, metadata) => {
  if (currentLogLevel >= LOG_LEVELS.DEBUG) {
    console.debug(formatLogMessage('DEBUG', message, metadata));
  }
};

export const logger = {
  error,
  warn,
  info,
  debug,
};

export default logger;
