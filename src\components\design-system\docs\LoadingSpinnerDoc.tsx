import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { LoadingSpinner } from '../../LoadingSpinner';

const LoadingSpinnerDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Loading Spinner"
      description="A cutting-edge loading animation inspired by the Resultid logo, providing visual feedback during data loading or processing operations. Features a dynamic beam animation that traces the outlines of the logo shapes with customizable sizes."
      importCode={`import { LoadingSpinner } from '../components/LoadingSpinner';`}
      usage={
        <div className="flex justify-center">
          <LoadingSpinner size="md" />
        </div>
      }
      code={`<LoadingSpinner size="md" />`}
      props={[
        {
          name: 'size',
          type: "'sm' | 'md' | 'lg'",
          default: 'md',
          description: 'Controls the size of the spinner.',
          required: false
        },
        {
          name: 'className',
          type: 'string',
          description: 'Additional CSS classes to apply to the component for custom styling.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Small Size',
          description: 'Compact spinner for inline loading indicators or tight spaces.',
          component: (
            <LoadingSpinner size="sm" />
          ),
          code: `<LoadingSpinner size="sm" />`
        },
        {
          title: 'Medium Size (Default)',
          description: 'Standard size for most loading scenarios.',
          component: (
            <LoadingSpinner size="md" />
          ),
          code: `<LoadingSpinner size="md" />`
        },
        {
          title: 'Large Size',
          description: 'Larger spinner for primary loading states or empty states.',
          component: (
            <LoadingSpinner size="lg" />
          ),
          code: `<LoadingSpinner size="lg" />`
        },
        {
          title: 'Custom Styling',
          description: 'Using the className prop to customize the spinner appearance.',
          component: (
            <LoadingSpinner 
              size="md"
              className="text-purple-500 dark:text-purple-400"
            />
          ),
          code: `<LoadingSpinner 
  size="md"
  className="text-purple-500 dark:text-purple-400"
/>`
        },
      ]}
      bestPractices={{
        do: [
          'Use to indicate that content is loading or an action is processing.',
          'Place the spinner in a container with adequate space to prevent layout shifts.',
          'Add descriptive text near the spinner when appropriate to explain what\'s loading.',
          'Use the appropriate size based on the context and available space.',
          'Consider using with a text description for better accessibility.'
        ],
        dont: [
          'Don\'t use spinners for very quick operations (under 300ms) to avoid flickering.',
          'Don\'t place multiple spinners close to each other as it can be distracting.',
          'Don\'t use overly large spinners that dominate the interface unnecessarily.',
          'Don\'t rely solely on the spinner for feedback without any textual explanation.',
          'Don\'t use spinners inconsistently across the application.'
        ]
      }}
      accessibility={[
        'Add appropriate aria-live regions around loading content for screen readers.',
        'Include descriptive text near the spinner to explain what\'s happening.',
        'Consider using aria-busy="true" on the parent container during loading states.',
        'For users with motion sensitivity, consider providing a reduced motion option.',
        'Ensure sufficient color contrast between the spinner and its background.',
        'Add a role="status" attribute when the spinner indicates a loading state.'
      ]}
    />
  );
};

export default LoadingSpinnerDoc;