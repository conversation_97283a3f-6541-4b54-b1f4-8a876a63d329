import React from 'react';
import ComponentDoc from '../ComponentDoc';

const ToastUtilsDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Toast Utilities"
      description="A set of utility functions for displaying toast notifications throughout the application. Provides a consistent interface for success, error, warning, and info messages."
      
      importCode={`import { showToast, successToast, errorToast, warningToast, infoToast } from '../utils/toastUtils';`}
      
      code={`// Show a basic toast with default settings
showToast('Your changes have been saved');

// Show a success toast with custom duration
successToast('Item created successfully', { duration: 5000 });

// Show an error toast with a title
errorToast('Unable to save changes', { title: 'Error' });

// Show a warning toast in a different position
warningToast('Your session will expire soon', { position: 'top-center' });

// Show an info toast without an icon
infoToast('New updates are available', { icon: false });`}
      
      props={[
        {
          name: 'showToast',
          type: `(message: string, type?: 'success' | 'error' | 'info' | 'warning', options?: { 
  duration?: number;
  title?: string;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  icon?: boolean;
  closable?: boolean;
}) => string`,
          description: 'Displays a toast notification with the specified type and options. Returns a unique ID that can be used to dismiss the toast.',
          required: false
        },
        {
          name: 'successToast',
          type: '(message: string, options?: object) => string',
          description: 'Shorthand for showing a success toast.',
          required: false
        },
        {
          name: 'errorToast',
          type: '(message: string, options?: object) => string',
          description: 'Shorthand for showing an error toast.',
          required: false
        },
        {
          name: 'warningToast',
          type: '(message: string, options?: object) => string',
          description: 'Shorthand for showing a warning toast.',
          required: false
        },
        {
          name: 'infoToast',
          type: '(message: string, options?: object) => string',
          description: 'Shorthand for showing an info toast.',
          required: false
        },
        {
          name: 'removeToast',
          type: '(id: string) => void',
          description: 'Removes a toast by its ID.',
          required: false
        }
      ]}
      
      variants={[
        {
          title: 'Success Toast',
          description: 'A toast notification indicating a successful operation.',
          component: (
            <div className="flex justify-end mb-4">
              <div className="rounded-md shadow-lg py-3 px-4 max-w-md transform scale-100 opacity-100 bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-800/50">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3">
                    <svg className="h-5 w-5 text-green-500 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1 pt-0.5">
                    <p className="font-medium">Success</p>
                    <p className="text-sm mt-1">Item created successfully</p>
                  </div>
                  <button className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none">
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="toast-progress h-1 absolute bottom-0 left-0 bg-current opacity-20 rounded-full" style={{ width: '100%', transformOrigin: 'left' }}></div>
              </div>
            </div>
          ),
          code: `successToast('Item created successfully', { 
  title: 'Success',
  duration: 3000
});`
        },
        {
          title: 'Error Toast',
          description: 'A toast notification indicating an error or failure.',
          component: (
            <div className="flex justify-end mb-4">
              <div className="rounded-md shadow-lg py-3 px-4 max-w-md transform scale-100 opacity-100 bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800/50">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3">
                    <svg className="h-5 w-5 text-red-500 dark:text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1 pt-0.5">
                    <p className="font-medium">Error</p>
                    <p className="text-sm mt-1">Unable to save changes. Please try again.</p>
                  </div>
                  <button className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none">
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="toast-progress h-1 absolute bottom-0 left-0 bg-current opacity-20 rounded-full" style={{ width: '100%', transformOrigin: 'left' }}></div>
              </div>
            </div>
          ),
          code: `errorToast('Unable to save changes. Please try again.', {
  title: 'Error',
  duration: 5000
});`
        },
        {
          title: 'Warning Toast',
          description: 'A toast notification for warnings or alerts.',
          component: (
            <div className="flex justify-end mb-4">
              <div className="rounded-md shadow-lg py-3 px-4 max-w-md transform scale-100 opacity-100 bg-amber-50 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-800/50">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3">
                    <svg className="h-5 w-5 text-amber-500 dark:text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1 pt-0.5">
                    <p className="text-sm">Your session will expire in 5 minutes. Please save your work.</p>
                  </div>
                  <button className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none">
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="toast-progress h-1 absolute bottom-0 left-0 bg-current opacity-20 rounded-full" style={{ width: '100%', transformOrigin: 'left' }}></div>
              </div>
            </div>
          ),
          code: `warningToast('Your session will expire in 5 minutes. Please save your work.', {
  position: 'top-center'
});`
        }
      ]}
      
      bestPractices={{
        do: [
          'Use appropriate toast types for different messages (success, error, warning, info)',
          'Keep messages concise and focused on a single piece of information',
          'Use titles to provide context for more complex notifications',
          'Position toasts consistently throughout the application',
          'Add progress indicators for toasts that will automatically dismiss'
        ],
        dont: [
          'Don\'t overuse toasts for non-critical information',
          'Don\'t display multiple toasts simultaneously for related actions',
          'Don\'t use long messages that will be cut off in the toast',
          'Don\'t set extremely short durations that prevent users from reading the message',
          'Don\'t use success toasts for actions that actually failed'
        ]
      }}
      
      accessibility={[
        'Toast notifications include appropriate ARIA roles and labels',
        'Progress indicator provides visual feedback about when the toast will disappear',
        'Close button allows users to dismiss notifications manually',
        'High contrast between text and background for all toast types',
        'Toasts never block important UI elements or functionality'
      ]}
    />
  );
};

export default ToastUtilsDoc;