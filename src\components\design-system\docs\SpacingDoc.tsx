import React from 'react';
import ComponentDoc from '../ComponentDoc';

const SpacingDoc: React.FC = () => {
  // Define spacing scale
  const spacingScale = [
    { name: '0', value: '0px', description: 'No spacing' },
    { name: '0.5', value: '0.125rem (2px)', description: 'Extra tiny spacing for tight constraints' },
    { name: '1', value: '0.25rem (4px)', description: 'Tiny spacing for very small elements' },
    { name: '1.5', value: '0.375rem (6px)', description: 'Very small spacing (e.g., between close elements)' },
    { name: '2', value: '0.5rem (8px)', description: 'Small spacing (e.g., between related elements)' },
    { name: '2.5', value: '0.625rem (10px)', description: 'Slightly larger than small spacing' },
    { name: '3', value: '0.75rem (12px)', description: 'Default spacing for related elements' },
    { name: '4', value: '1rem (16px)', description: 'Medium spacing for component spacing' },
    { name: '5', value: '1.25rem (20px)', description: 'Slightly larger medium spacing' },
    { name: '6', value: '1.5rem (24px)', description: 'Large component spacing' },
    { name: '8', value: '2rem (32px)', description: 'Extra large spacing between components' },
    { name: '10', value: '2.5rem (40px)', description: 'Very large spacing for section separation' },
    { name: '12', value: '3rem (48px)', description: 'Huge spacing for major section breaks' },
    { name: '16', value: '4rem (64px)', description: 'Extra huge spacing for layout sections' },
    { name: '20', value: '5rem (80px)', description: 'Extreme spacing for major layout sections' },
    { name: '24', value: '6rem (96px)', description: 'Maximum spacing for page sections' },
  ];

  // Common spacing use cases
  const spacingUseCases = [
    { type: 'Padding', small: 'p-2 (0.5rem)', medium: 'p-4 (1rem)', large: 'p-6 (1.5rem)', description: 'Space inside components' },
    { type: 'Margin', small: 'm-2 (0.5rem)', medium: 'm-4 (1rem)', large: 'm-6 (1.5rem)', description: 'Space between components' },
    { type: 'Gap', small: 'gap-2 (0.5rem)', medium: 'gap-4 (1rem)', large: 'gap-6 (1.5rem)', description: 'Space between grid/flex items' },
    { type: 'Space Between', small: 'space-y-2 (0.5rem)', medium: 'space-y-4 (1rem)', large: 'space-y-6 (1.5rem)', description: 'Vertical spacing between child elements' },
  ];

  return (
    <ComponentDoc
      title="Spacing"
      description="A consistent spacing system for layouts, components, and elements across the Resultid platform."
      noImport={true}
      noBestPractices={false}
      customContent={
        <div className="space-y-12">
          <section id="overview">
            <h2 className="text-xl font-semibold mb-4">Overview</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our spacing system provides a consistent scale for margins, padding, and positioning elements throughout 
              the interface. It helps create visual rhythm, establishes hierarchy, and improves the overall usability 
              of the platform. The spacing system is based on a 4px (0.25rem) base unit, providing flexibility while 
              maintaining visual consistency.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Consistency</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  A systematic spacing scale creates visual consistency across the platform, 
                  making the interface feel cohesive and professionally designed.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Hierarchy</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Different spacing values create visual hierarchy, helping users understand 
                  the relationship between elements and sections.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Responsiveness</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Our spacing system adapts to different screen sizes, ensuring comfortable 
                  spacing across devices while maintaining visual relationships.
                </p>
              </div>
            </div>
          </section>

          <section id="spacing-scale">
            <h2 className="text-xl font-semibold mb-4">Spacing Scale</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our spacing scale is built on a 4px (0.25rem) base unit, providing a comprehensive range 
              of values for all spacing needs. This scale is implemented using Tailwind CSS utility classes.
            </p>
            
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Spacing Token
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Value
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Example
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Description
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {spacingScale.map((spacing, index) => (
                      <tr key={index} className={index % 2 === 0 ? "bg-white dark:bg-gray-900" : "bg-gray-50 dark:bg-gray-800"}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {spacing.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {spacing.value}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-4 bg-blue-500 dark:bg-blue-600" style={{ width: `${Math.min(parseFloat(spacing.name) * 4, 96)}px` }}></div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {spacing.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </section>

          <section id="tailwind-usage">
            <h2 className="text-xl font-semibold mb-4">Usage with Tailwind CSS</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our spacing system is implemented using Tailwind CSS utility classes. These classes can be 
              applied for margins, padding, gap, and other spacing properties.
            </p>
            
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Usage Type
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Small
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Medium
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Large
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Description
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {spacingUseCases.map((useCase, index) => (
                      <tr key={index} className={index % 2 === 0 ? "bg-white dark:bg-gray-900" : "bg-gray-50 dark:bg-gray-800"}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {useCase.type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {useCase.small}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {useCase.medium}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {useCase.large}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {useCase.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            
            <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Tailwind CSS Spacing Utilities</h3>
              
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Directional Padding</h4>
                <div className="mb-4 rounded-md bg-gray-900 p-4">
                  <pre className="text-white text-sm"><code>{`<div className="p-4">All sides padding (1rem)</div>
<div className="px-4">Horizontal padding (left and right)</div>
<div className="py-4">Vertical padding (top and bottom)</div>
<div className="pt-4">Top padding only</div>
<div className="pr-4">Right padding only</div>
<div className="pb-4">Bottom padding only</div>
<div className="pl-4">Left padding only</div>`}</code></pre>
                </div>
              </div>
              
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Directional Margin</h4>
                <div className="mb-4 rounded-md bg-gray-900 p-4">
                  <pre className="text-white text-sm"><code>{`<div className="m-4">All sides margin (1rem)</div>
<div className="mx-4">Horizontal margin (left and right)</div>
<div className="my-4">Vertical margin (top and bottom)</div>
<div className="mt-4">Top margin only</div>
<div className="mr-4">Right margin only</div>
<div className="mb-4">Bottom margin only</div>
<div className="ml-4">Left margin only</div>`}</code></pre>
                </div>
              </div>
              
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Gap (for Flex and Grid)</h4>
                <div className="mb-4 rounded-md bg-gray-900 p-4">
                  <pre className="text-white text-sm"><code>{`<div className="flex gap-4">Gap between all flex items</div>
<div className="flex gap-x-4">Horizontal gap between flex items</div>
<div className="flex gap-y-4">Vertical gap between flex items</div>

<div className="grid grid-cols-3 gap-4">Gap between all grid items</div>
<div className="grid grid-cols-3 gap-x-4">Horizontal gap between grid items</div>
<div className="grid grid-cols-3 gap-y-4">Vertical gap between grid items</div>`}</code></pre>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Space Between</h4>
                <div className="mb-4 rounded-md bg-gray-900 p-4">
                  <pre className="text-white text-sm"><code>{`<div className="space-y-4">
  <div>First child</div>
  <div>Second child (4 units of space above)</div>
  <div>Third child (4 units of space above)</div>
</div>

<div className="space-x-4">
  <span>First inline child</span>
  <span>Second inline child (4 units of space to the left)</span>
  <span>Third inline child (4 units of space to the left)</span>
</div>`}</code></pre>
                </div>
              </div>
            </div>
          </section>

          <section id="spacing-components">
            <h2 className="text-xl font-semibold mb-4">Spacing in Components</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Different UI components require specific spacing to maintain usability and visual harmony. 
              Here are some common spacing patterns used in our component system.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Card Component</h3>
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <div className="relative">
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="h-full w-full flex flex-col">
                        <div className="h-6 bg-blue-200/20 dark:bg-blue-800/20 border-b border-dashed border-blue-500/40 flex items-center justify-center">
                          <span className="text-xs text-blue-600 dark:text-blue-400">p-6 (top)</span>
                        </div>
                        <div className="flex-1 flex">
                          <div className="w-6 bg-blue-200/20 dark:bg-blue-800/20 border-r border-dashed border-blue-500/40 flex items-center justify-center">
                            <span className="text-xs text-blue-600 dark:text-blue-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-6 (left)</span>
                          </div>
                          <div className="flex-1"></div>
                          <div className="w-6 bg-blue-200/20 dark:bg-blue-800/20 border-l border-dashed border-blue-500/40 flex items-center justify-center">
                            <span className="text-xs text-blue-600 dark:text-blue-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-6 (right)</span>
                          </div>
                        </div>
                        <div className="h-6 bg-blue-200/20 dark:bg-blue-800/20 border-t border-dashed border-blue-500/40 flex items-center justify-center">
                          <span className="text-xs text-blue-600 dark:text-blue-400">p-6 (bottom)</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Card Title</h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-4">
                        This is a typical card component with standard padding and spacing between elements.
                      </p>
                      <div className="relative">
                        <div className="absolute -inset-y-2 -left-2 w-4 pointer-events-none bg-amber-200/20 dark:bg-amber-800/20 border-r border-dashed border-amber-500/40 flex items-center justify-center">
                          <span className="text-xs text-amber-600 dark:text-amber-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>my-4</span>
                        </div>
                      </div>
                      <button className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md">Action Button</button>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Card Spacing Notes</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Outer padding: p-6 (1.5rem) for comfortable spacing</li>
                    <li>Space between title and content: mb-2 (0.5rem)</li>
                    <li>Space between content and actions: mb-4 (1rem)</li>
                    <li>Button internal padding: px-4 py-2 (horizontal: 1rem, vertical: 0.5rem)</li>
                  </ul>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Form Component</h3>
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <div className="relative">
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="h-full w-full flex flex-col">
                        <div className="h-6 bg-blue-200/20 dark:bg-blue-800/20 border-b border-dashed border-blue-500/40 flex items-center justify-center">
                          <span className="text-xs text-blue-600 dark:text-blue-400">p-6 (top)</span>
                        </div>
                        <div className="flex-1 flex">
                          <div className="w-6 bg-blue-200/20 dark:bg-blue-800/20 border-r border-dashed border-blue-500/40 flex items-center justify-center">
                            <span className="text-xs text-blue-600 dark:text-blue-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-6 (left)</span>
                          </div>
                          <div className="flex-1"></div>
                          <div className="w-6 bg-blue-200/20 dark:bg-blue-800/20 border-l border-dashed border-blue-500/40 flex items-center justify-center">
                            <span className="text-xs text-blue-600 dark:text-blue-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-6 (right)</span>
                          </div>
                        </div>
                        <div className="h-6 bg-blue-200/20 dark:bg-blue-800/20 border-t border-dashed border-blue-500/40 flex items-center justify-center">
                          <span className="text-xs text-blue-600 dark:text-blue-400">p-6 (bottom)</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Form Title</h3>
                      
                      <div className="relative">
                        <div className="absolute -left-2 inset-y-0 w-4 pointer-events-none bg-green-200/20 dark:bg-green-800/20 border-r border-dashed border-green-500/40 flex items-center justify-center">
                          <span className="text-xs text-green-600 dark:text-green-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>space-y-4</span>
                        </div>
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name</label>
                            <input type="text" className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                            <input type="email" className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Message</label>
                            <textarea className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md" rows={3}></textarea>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-6 flex justify-end">
                        <button className="px-4 py-2 bg-blue-600 text-white rounded-md">Submit</button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Form Spacing Notes</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Outer padding: p-6 (1.5rem) for comfortable spacing</li>
                    <li>Space between title and form: mb-4 (1rem)</li>
                    <li>Space between form groups: space-y-4 (1rem)</li>
                    <li>Space between label and input: mb-1 (0.25rem)</li>
                    <li>Input internal padding: px-3 py-2 (horizontal: 0.75rem, vertical: 0.5rem)</li>
                    <li>Space before button section: mt-6 (1.5rem)</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <section id="spacing-layouts">
            <h2 className="text-xl font-semibold mb-4">Spacing in Layouts</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Consistent spacing in layouts creates visual harmony and improves the overall user experience.
              Here are some common layout spacing patterns used across the platform.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Page Layout</h3>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-48"></div>
                  </div>
                  <div className="bg-white dark:bg-gray-900 relative">
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="h-full w-full flex flex-col">
                        <div className="h-8 bg-purple-200/20 dark:bg-purple-800/20 border-b border-dashed border-purple-500/40 flex items-center justify-center">
                          <span className="text-xs text-purple-600 dark:text-purple-400">p-8 (top)</span>
                        </div>
                        <div className="flex-1 flex">
                          <div className="w-8 bg-purple-200/20 dark:bg-purple-800/20 border-r border-dashed border-purple-500/40 flex items-center justify-center">
                            <span className="text-xs text-purple-600 dark:text-purple-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-8 (left)</span>
                          </div>
                          <div className="flex-1"></div>
                          <div className="w-8 bg-purple-200/20 dark:bg-purple-800/20 border-l border-dashed border-purple-500/40 flex items-center justify-center">
                            <span className="text-xs text-purple-600 dark:text-purple-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-8 (right)</span>
                          </div>
                        </div>
                        <div className="h-8 bg-purple-200/20 dark:bg-purple-800/20 border-t border-dashed border-purple-500/40 flex items-center justify-center">
                          <span className="text-xs text-purple-600 dark:text-purple-400">p-8 (bottom)</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-8 flex flex-col min-h-[260px]">
                      <div className="mb-6">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Page Title</h2>
                        <p className="text-gray-600 dark:text-gray-300">Page description and intro text goes here.</p>
                      </div>
                      
                      <div className="relative flex-1">
                        <div className="absolute -left-2 inset-y-0 w-4 pointer-events-none bg-cyan-200/20 dark:bg-cyan-800/20 border-r border-dashed border-cyan-500/40 flex items-center justify-center">
                          <span className="text-xs text-cyan-600 dark:text-cyan-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>gap-6</span>
                        </div>
                        <div className="grid grid-cols-2 gap-6 h-full">
                          <div className="border border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 p-4 flex items-center justify-center">
                            <span className="text-gray-500 dark:text-gray-400">Content Area</span>
                          </div>
                          <div className="border border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 p-4 flex items-center justify-center">
                            <span className="text-gray-500 dark:text-gray-400">Content Area</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Page Layout Spacing Notes</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Page content padding: p-8 (2rem) for breathing room</li>
                    <li>Space between header and content: mb-6 (1.5rem)</li>
                    <li>Space between title and description: mb-2 (0.5rem)</li>
                    <li>Grid gap between content areas: gap-6 (1.5rem)</li>
                    <li>Content area padding: p-4 (1rem)</li>
                  </ul>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Section Layout</h3>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <div className="bg-white dark:bg-gray-900 relative">
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="h-full w-full flex flex-col">
                        <div className="h-6 bg-indigo-200/20 dark:bg-indigo-800/20 border-b border-dashed border-indigo-500/40 flex items-center justify-center">
                          <span className="text-xs text-indigo-600 dark:text-indigo-400">p-6 (top)</span>
                        </div>
                        <div className="flex-1 flex">
                          <div className="w-6 bg-indigo-200/20 dark:bg-indigo-800/20 border-r border-dashed border-indigo-500/40 flex items-center justify-center">
                            <span className="text-xs text-indigo-600 dark:text-indigo-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-6 (left)</span>
                          </div>
                          <div className="flex-1"></div>
                          <div className="w-6 bg-indigo-200/20 dark:bg-indigo-800/20 border-l border-dashed border-indigo-500/40 flex items-center justify-center">
                            <span className="text-xs text-indigo-600 dark:text-indigo-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>p-6 (right)</span>
                          </div>
                        </div>
                        <div className="h-6 bg-indigo-200/20 dark:bg-indigo-800/20 border-t border-dashed border-indigo-500/40 flex items-center justify-center">
                          <span className="text-xs text-indigo-600 dark:text-indigo-400">p-6 (bottom)</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-6 min-h-[300px]">
                      <div className="mb-5 flex justify-between items-center">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Section Title</h3>
                        <button className="text-sm text-blue-600 dark:text-blue-400">View All</button>
                      </div>
                      
                      <div className="relative">
                        <div className="absolute -left-2 inset-y-0 w-4 pointer-events-none bg-pink-200/20 dark:bg-pink-800/20 border-r border-dashed border-pink-500/40 flex items-center justify-center">
                          <span className="text-xs text-pink-600 dark:text-pink-400 rotate-180" style={{ writingMode: 'vertical-rl' }}>space-y-4</span>
                        </div>
                        <div className="space-y-4">
                          {[1, 2, 3].map((item) => (
                            <div key={item} className="border border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 p-3 flex items-center justify-between">
                              <span className="text-gray-700 dark:text-gray-300">List Item {item}</span>
                              <span className="text-sm text-gray-500 dark:text-gray-400">Details</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="mt-6 text-center">
                        <button className="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md border border-gray-200 dark:border-gray-700">
                          Load More
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Section Layout Spacing Notes</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Section padding: p-6 (1.5rem)</li>
                    <li>Space between header and content: mb-5 (1.25rem)</li>
                    <li>Space between list items: space-y-4 (1rem)</li>
                    <li>List item padding: p-3 (0.75rem)</li>
                    <li>Space before footer button: mt-6 (1.5rem)</li>
                    <li>Button padding: px-4 py-2 (horizontal: 1rem, vertical: 0.5rem)</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <section id="responsive-spacing">
            <h2 className="text-xl font-semibold mb-4">Responsive Spacing</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our spacing system adapts to different screen sizes, providing appropriate spacing 
              across devices while maintaining visual relationships between elements.
            </p>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Responsive Spacing Examples</h3>
              
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Adaptive Container Padding</h4>
                <div className="mb-4 rounded-md bg-gray-900 p-4">
                  <pre className="text-white text-sm"><code>{`<!-- Container with responsive padding -->
<div className="p-4 md:p-6 lg:p-8">
  <!-- Content goes here -->
</div>`}</code></pre>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  This container has 1rem padding on mobile, 1.5rem on tablets, and 2rem on desktop screens.
                </p>
              </div>
              
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Responsive Gaps in Grid Layouts</h4>
                <div className="mb-4 rounded-md bg-gray-900 p-4">
                  <pre className="text-white text-sm"><code>{`<!-- Grid with responsive columns and gap -->
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</div>`}</code></pre>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  This grid layout has 1 column on mobile with 1rem gap, 2 columns on tablets with 1.5rem gap, 
                  and 3 columns on desktop screens with 1.5rem gap.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Responsive Margins Between Sections</h4>
                <div className="mb-4 rounded-md bg-gray-900 p-4">
                  <pre className="text-white text-sm"><code>{`<!-- Section with responsive margins -->
<section className="my-8 md:my-12 lg:my-16">
  <h2>Section Title</h2>
  <!-- Content goes here -->
</section>`}</code></pre>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  This section has 2rem vertical margin on mobile, 3rem on tablets, and 4rem on desktop screens,
                  providing appropriate breathing room as screen size increases.
                </p>
              </div>
            </div>
          </section>

          <section id="best-practices">
            <h2 className="text-xl font-semibold mb-4">Best Practices</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-900/30 rounded-lg p-4">
                <h4 className="font-medium text-green-800 dark:text-green-400 mb-2">Do</h4>
                <ul className="pl-5 list-disc space-y-2 text-gray-600 dark:text-gray-300">
                  <li>Use the defined spacing scale values consistently</li>
                  <li>Apply appropriate responsive spacing based on screen size</li>
                  <li>Use larger spacing for separating distinct sections</li>
                  <li>Use smaller spacing for related elements within a group</li>
                  <li>Maintain consistent spacing patterns within similar components</li>
                  <li>Use spacing to establish clear visual hierarchy</li>
                </ul>
              </div>
              
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-900/30 rounded-lg p-4">
                <h4 className="font-medium text-red-800 dark:text-red-400 mb-2">Don't</h4>
                <ul className="pl-5 list-disc space-y-2 text-gray-600 dark:text-gray-300">
                  <li>Create arbitrary spacing values outside the system</li>
                  <li>Use inconsistent spacing for similar elements</li>
                  <li>Overcrowd elements with insufficient spacing</li>
                  <li>Use excessive spacing that creates disconnected layouts</li>
                  <li>Neglect responsive spacing adjustments for different devices</li>
                  <li>Mix spacing units (e.g., rem and pixels) inconsistently</li>
                </ul>
              </div>
            </div>
          </section>
        </div>
      }
    />
  );
};

export default SpacingDoc;