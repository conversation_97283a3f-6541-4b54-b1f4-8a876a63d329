import axios from 'axios';
import { Notification, NotificationType, NotificationPriority } from '../types/notificationTypes';

interface SMSSubscription {
  id: string;
  userId: string;
  phoneNumber: string;
  notificationTypes: NotificationType[];
  priorities: NotificationPriority[];
  categories?: string[];
  enabled: boolean;
}

/**
 * Service to handle SMS notification checks and sending
 */
export class SMSNotificationService {
  private static instance: SMSNotificationService;
  private subscriptions: SMSSubscription | null = null;
  private isLoading: boolean = false;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

  private constructor() {
    // Private constructor to enforce singleton
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): SMSNotificationService {
    if (!SMSNotificationService.instance) {
      SMSNotificationService.instance = new SMSNotificationService();
    }
    return SMSNotificationService.instance;
  }

  /**
   * Fetch the user's SMS subscription
   */
  public async fetchSubscription(): Promise<SMSSubscription | null> {
    // Don't fetch if we're already loading or if we have a recent cache
    const now = Date.now();
    if (
      this.isLoading || 
      (this.subscriptions && now - this.lastFetchTime < this.CACHE_DURATION)
    ) {
      return this.subscriptions;
    }

    try {
      this.isLoading = true;
      
      // Get token from localStorage
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.log('No auth token found, skipping SMS subscription fetch');
        return null;
      }

      const response = await axios.get('/api/notifications/sms-subscription', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success && response.data.data) {
        this.subscriptions = response.data.data;
        this.lastFetchTime = now;
        return this.subscriptions;
      }
      
      return null;
    } catch (error) {
      // It's okay if there's no subscription yet
      console.log('No SMS subscription found or error fetching:', error);
      return null;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Check if a notification matches the subscription criteria
   */
  public async checkNotificationForSMS(notification: Notification): Promise<boolean> {
    try {
      // Fetch the latest subscription
      const subscription = await this.fetchSubscription();
      
      // If no subscription or not enabled, skip
      if (!subscription || !subscription.enabled) {
        return false;
      }

      // Check if notification type matches
      const typeMatches = subscription.notificationTypes.includes(notification.type);
      
      // Check if priority matches
      const priorityMatches = subscription.priorities.includes(notification.priority || 'medium');
      
      // Check category if specified in both notification and subscription
      let categoryMatches = true;
      if (
        subscription.categories && 
        subscription.categories.length > 0 && 
        notification.category
      ) {
        categoryMatches = subscription.categories.includes(notification.category);
      }

      // Return true if all criteria match
      return typeMatches && priorityMatches && categoryMatches;
    } catch (error) {
      console.error('Error checking notification for SMS:', error);
      return false;
    }
  }

  /**
   * Send an SMS for a notification
   */
  public async sendSMSForNotification(notification: Notification): Promise<boolean> {
    try {
      // Check if notification matches criteria
      const shouldSend = await this.checkNotificationForSMS(notification);
      
      if (!shouldSend) {
        return false;
      }

      // Get token from localStorage
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.error('No auth token found, cannot send SMS');
        return false;
      }

      // Format the notification content
      let messageContent = '';
      
      // Add title if available
      if (notification.title) {
        messageContent += `${notification.title}\n\n`;
      }
      
      // Add content
      if (typeof notification.content === 'string') {
        messageContent += notification.content;
      } else if (notification.content && notification.content.text) {
        messageContent += notification.content.text;
      } else if (notification.content) {
        messageContent += JSON.stringify(notification.content);
      }

      // Send the SMS via the API
      const response = await axios.post(
        '/api/notifications/send-sms-for-notification',
        {
          notificationId: notification.id,
          content: messageContent
        },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      return response.data.success;
    } catch (error) {
      console.error('Error sending SMS for notification:', error);
      return false;
    }
  }
}
