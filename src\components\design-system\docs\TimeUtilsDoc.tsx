import React from 'react';
import ComponentDoc from '../ComponentDoc';

const TimeUtilsDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Time Utilities"
      description="Utility functions for detecting and validating time-related values and columns in datasets. These utilities help identify date and time columns automatically to improve data processing and visualization."
      
      importCode={`import { isTimeColumn, isTimeValue } from '../utils/timeUtils';`}
      
      code={`// Check if a column is a time/date column based on sample values
const sampleValues = ['2023-05-15', '2023-06-20', '2023-07-10', '2023-08-05', '2023-09-12'];
const isTimeCol = isTimeColumn('date_created', sampleValues);
console.log(isTimeCol); // true

// Check if a specific value is a time/date value
const isTime = isTimeValue('2023-05-15T14:30:00Z');
console.log(isTime); // true

// These utilities help with automatic column type detection
function detectColumnTypes(columns, sampleData) {
  const detectedTypes = {};
  
  columns.forEach(column => {
    const samples = sampleData.map(row => row[column]);
    
    if (isTimeColumn(column, samples)) {
      detectedTypes[column] = 'datetime';
    } else {
      // Other type detection logic...
    }
  });
  
  return detectedTypes;
}`}
      
      props={[
        {
          name: 'isTimeColumn',
          type: '(columnName: string, sampleValues: any[]) => boolean',
          description: 'Determines if a column contains time/date values based on a sample of values. Requires at least 5 values to match a time pattern.',
          required: false
        },
        {
          name: 'isTimeValue',
          type: '(value: { toString: () => string }) => boolean',
          description: 'Checks if a single value is a time/date value by matching it against common time patterns.',
          required: false
        }
      ]}
      
      variants={[
        {
          title: 'Time Column Detection',
          description: 'Detecting a column that contains date/time values.',
          component: (
            <div className="space-y-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Input:</h3>
                <div className="mt-2 bg-gray-50 dark:bg-gray-900 p-3 rounded text-sm font-mono">
                  <p>columnName: "date_created"</p>
                  <p>sampleValues: [</p>
                  <p className="pl-4">"2023-01-15",</p>
                  <p className="pl-4">"2023-02-20",</p>
                  <p className="pl-4">"2023-03-10",</p>
                  <p className="pl-4">"2023-04-05",</p>
                  <p className="pl-4">"2023-05-12"</p>
                  <p>]</p>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Result:</h3>
                <div className="mt-2 bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200 p-3 rounded text-sm">
                  <p>true <span className="text-gray-500 dark:text-gray-400">(Column is detected as a time column)</span></p>
                </div>
              </div>
            </div>
          ),
          code: `const columnName = 'date_created';
const sampleValues = [
  '2023-01-15',
  '2023-02-20',
  '2023-03-10',
  '2023-04-05',
  '2023-05-12'
];

const isTime = isTimeColumn(columnName, sampleValues);
console.log(isTime); // true`
        },
        {
          title: 'Mixed Value Column Detection',
          description: 'A column with some time values and some non-time values.',
          component: (
            <div className="space-y-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Input:</h3>
                <div className="mt-2 bg-gray-50 dark:bg-gray-900 p-3 rounded text-sm font-mono">
                  <p>columnName: "mixed_column"</p>
                  <p>sampleValues: [</p>
                  <p className="pl-4">"2023-01-15",</p>
                  <p className="pl-4">"ABC123",</p>
                  <p className="pl-4">"2023-03-10",</p>
                  <p className="pl-4">"XYZ456",</p>
                  <p className="pl-4">"2023-05-12"</p>
                  <p>]</p>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Result:</h3>
                <div className="mt-2 bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200 p-3 rounded text-sm">
                  <p>false <span className="text-gray-500 dark:text-gray-400">(Not enough matching time values to be classified as a time column)</span></p>
                </div>
              </div>
            </div>
          ),
          code: `const columnName = 'mixed_column';
const sampleValues = [
  '2023-01-15',
  'ABC123',     // Not a time value
  '2023-03-10',
  'XYZ456',     // Not a time value
  '2023-05-12'
];

const isTime = isTimeColumn(columnName, sampleValues);
console.log(isTime); // false (only 3 out of 5 match, needs at least 5)`
        },
        {
          title: 'Individual Time Value Validation',
          description: 'Checking if individual values are time values.',
          component: (
            <div className="space-y-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <table className="w-full text-left">
                <thead>
                  <tr>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Value</th>
                    <th className="pb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Result</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">2023-05-15T14:30:00Z</td>
                    <td className="py-2 text-sm font-medium text-green-600 dark:text-green-400">true</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">05/15/2023 2:30 PM</td>
                    <td className="py-2 text-sm font-medium text-green-600 dark:text-green-400">true</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">2023-05-15</td>
                    <td className="py-2 text-sm font-medium text-green-600 dark:text-green-400">true</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">last Monday</td>
                    <td className="py-2 text-sm font-medium text-green-600 dark:text-green-400">true</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-900 dark:text-white">ABC123</td>
                    <td className="py-2 text-sm font-medium text-red-600 dark:text-red-400">false</td>
                  </tr>
                </tbody>
              </table>
            </div>
          ),
          code: `// ISO format
console.log(isTimeValue('2023-05-15T14:30:00Z')); // true

// US date format
console.log(isTimeValue('05/15/2023 2:30 PM')); // true

// Date only
console.log(isTimeValue('2023-05-15')); // true

// Relative time
console.log(isTimeValue('last Monday')); // true

// Not a time value
console.log(isTimeValue('ABC123')); // false`
        }
      ]}
      
      bestPractices={{
        do: [
          'Use these utilities for automatic detection of date/time columns during data import',
          'Provide a way for users to override automatic detection when needed',
          'Include a good sample size of values (at least 5-10 rows) for more accurate detection',
          'Consider the column name when detecting time columns (names containing "date", "time", etc.)',
          'Use the isTimeValue function to validate individual values when needed'
        ],
        dont: [
          'Don\'t rely solely on these utilities without user verification for critical data processing',
          'Don\'t assume all values in a column will match the detected pattern',
          'Don\'t use a small sample size for detection (need at least 5 matching values)',
          'Don\'t forget to check for null or undefined values before passing to these functions',
          'Don\'t expect perfect detection for unusual date/time formats'
        ]
      }}
      
      accessibility={[
        'Proper date/time detection helps with correct data visualization and sorting',
        'Consistent date formatting improves readability for all users',
        'Accurate time column detection enables better filtering and search capabilities',
        'Automatic detection reduces manual user input, lowering cognitive load',
        'These utilities help ensure dates are treated semantically as dates in the application'
      ]}
    />
  );
};

export default TimeUtilsDoc;