import React from 'react';
import { useNotification } from '../../contexts/NotificationContext';
import { NotificationItem } from './NotificationItem';

interface NotificationContainerProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  maxNotifications?: number;
}

export const NotificationContainer: React.FC<NotificationContainerProps> = ({
  position = 'top-right',
  maxNotifications = 5
}) => {
  const { notifications, removeNotification } = useNotification();
  
  // Only show the most recent notifications up to maxNotifications
  const visibleNotifications = notifications
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, maxNotifications);
  
  // Position-specific styles
  const getPositionStyles = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      default:
        return 'top-4 right-4';
    }
  };
  
  if (visibleNotifications.length === 0) {
    return null;
  }
  
  return (
    <div 
      className={`fixed z-50 w-80 max-w-full ${getPositionStyles()}`}
      aria-live="polite"
    >
      {visibleNotifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onDismiss={removeNotification}
        />
      ))}
    </div>
  );
};
