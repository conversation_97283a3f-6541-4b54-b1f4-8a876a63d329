/**
 * Tool Registry System
 * Central registry for all agent tools with discovery and permission management
 */

import { BaseTool, ToolCategory } from './tools/baseTool';

/**
 * A registry that manages all available tools for the agent
 */
export class ToolRegistry {
  private tools: Map<string, BaseTool<any, any>> = new Map();
  private static instance: ToolRegistry | null = null;

  /**
   * Get the singleton instance of the tool registry
   */
  public static getInstance(): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry();
    }
    return ToolRegistry.instance;
  }

  /**
   * Register a tool with the registry
   * @param tool Tool to register
   * @throws Error if a tool with the same ID is already registered
   */
  public registerTool(tool: BaseTool<any, any>): void {
    if (this.tools.has(tool.id)) {
      throw new Error(`Tool with ID ${tool.id} is already registered`);
    }
    this.tools.set(tool.id, tool);
  }

  /**
   * Unregister a tool from the registry
   * @param toolId ID of the tool to unregister
   * @returns true if the tool was unregistered, false if not found
   */
  public unregisterTool(toolId: string): boolean {
    return this.tools.delete(toolId);
  }

  /**
   * Get a tool by ID
   * @param toolId ID of the tool to retrieve
   * @returns The tool if found, undefined otherwise
   */
  public getTool(toolId: string): BaseTool<any, any> | undefined {
    return this.tools.get(toolId);
  }

  /**
   * Get all registered tools
   * @returns Array of all registered tools
   */
  public getAllTools(): BaseTool<any, any>[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get tools by category
   * @param category Category to filter by
   * @returns Array of tools in the specified category
   */
  public getToolsByCategory(category: ToolCategory): BaseTool<any, any>[] {
    return Array.from(this.tools.values())
      .filter(tool => tool.category === category);
  }

  /**
   * Get all tools that the user has permission to use
   * @param userPermissions Array of permission strings the user has
   * @returns Array of tools the user can access
   */
  public getAvailableTools(userPermissions: string[]): BaseTool<any, any>[] {
    return Array.from(this.tools.values())
      .filter(tool => this.hasPermission(tool, userPermissions));
  }

  /**
   * Check if a user has permission to use a tool
   * @param tool Tool to check
   * @param userPermissions User's permissions
   * @returns true if the user has permission, false otherwise
   */
  private hasPermission(tool: BaseTool<any, any>, userPermissions: string[]): boolean {
    // Public tools don't require specific permissions
    if (!tool.requiresAuth || tool.permissions.length === 0) {
      return true;
    }

    // Check if user has at least one of the required permissions
    return tool.permissions.some(permission => 
      userPermissions.includes(permission)
    );
  }

  /**
   * Find tools that match a specific capability or use case
   * @param query Search query for tool discovery
   * @returns Array of matching tools
   */
  public findToolsByCapability(query: string): BaseTool<any, any>[] {
    const lowercaseQuery = query.toLowerCase();
    
    return Array.from(this.tools.values()).filter(tool => {
      // Search in tool name, description, and category
      return (
        tool.name.toLowerCase().includes(lowercaseQuery) ||
        tool.description.toLowerCase().includes(lowercaseQuery) ||
        tool.category.toString().toLowerCase().includes(lowercaseQuery)
      );
    });
  }

  /**
   * Format tool definitions for inclusion in LLM system prompts
   * @param tools Array of tools to format
   * @returns Formatted string describing the tools
   */
  public formatToolsForPrompt(tools: BaseTool<any, any>[]): string {
    return tools.map(tool => {
      const examples = tool.getExamples();
      const examplesText = examples.length > 0
        ? `\nEXAMPLES:\n${examples.map((ex, i) => 
            `${i + 1}. ${ex.description}:\n${JSON.stringify(ex.params, null, 2)}`
          ).join('\n\n')}`
        : '';

      return `TOOL: ${tool.name} (${tool.id})
DESCRIPTION: ${tool.description}
WHEN TO USE: ${tool.getHelpText()}
PARAMETERS: ${JSON.stringify(this.getToolParameterSchema(tool), null, 2)}${examplesText}`;
    }).join('\n\n');
  }

  /**
   * Get parameter schema for a tool (simplified for prompt inclusion)
   * @param tool Tool to extract parameters from
   * @returns Object representing parameter schema
   */
  private getToolParameterSchema(tool: BaseTool<any, any>): any {
    // This is a simplified approach - a real implementation would use 
    // reflection or TypeScript type information to extract parameter schema
    
    // For now, we'll use a sample from the first example if available
    const examples = tool.getExamples();
    if (examples.length > 0) {
      return examples[0].params;
    }
    
    // Fallback - return empty object
    return {};
  }
}

export default ToolRegistry.getInstance();