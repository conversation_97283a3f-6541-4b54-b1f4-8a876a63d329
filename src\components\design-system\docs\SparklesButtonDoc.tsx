import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { SparklesButton } from '../../ui/SparklesButton';
import { Wand2 } from 'lucide-react';

const SparklesButtonDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Sparkles Button"
      description="A decorative button with animated sparkle effects that provides visual feedback and draws attention to AI-related actions."
      importCode={`import { SparklesButton } from '../components/ui/SparklesButton';`}
      usage={
        <SparklesButton
          onClick={() => alert('Sparkles Button clicked!')}
          title="Generate with AI"
        >
          <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
            <Wand2 className="h-5 w-5" />
          </div>
        </SparklesButton>
      }
      code={`<SparklesButton
  onClick={() => handleGenerateWithAI()}
  title="Generate with AI"
>
  <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
    <Wand2 className="h-5 w-5" />
  </div>
</SparklesButton>`}
      props={[
        {
          name: 'children',
          type: 'React.ReactNode',
          description: 'The content to be displayed inside the button (usually an icon).',
          required: true
        },
        {
          name: 'onClick',
          type: '() => void',
          description: 'Function to be called when the button is clicked.',
          required: true
        },
        {
          name: 'disabled',
          type: 'boolean',
          default: 'false',
          description: 'Disables the button when set to true.',
          required: false
        },
        {
          name: 'className',
          type: 'string',
          description: 'Additional CSS classes to apply to the button.',
          required: false
        },
        {
          name: 'title',
          type: 'string',
          default: '"Generate with AI"',
          description: 'Text for the tooltip that appears on hover.',
          required: false
        },
        {
          name: 'sparklesCount',
          type: 'number',
          default: '10',
          description: 'Maximum number of sparkles to display at once.',
          required: false
        },
        {
          name: 'colors',
          type: '{ first: string; second: string }',
          default: '{ first: "#A07CFE", second: "#FE8FB5" }',
          description: 'Custom colors for the sparkles.',
          required: false
        }
      ]}
      variants={[
        {
          title: 'Default Sparkles Button',
          description: 'The standard implementation with default sparkle colors.',
          component: (
            <SparklesButton onClick={() => alert('Default Sparkles Button clicked!')} title="Generate with AI">
              <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                <Wand2 className="h-5 w-5" />
              </div>
            </SparklesButton>
          ),
          code: `<SparklesButton onClick={handleClick} title="Generate with AI">
  <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
    <Wand2 className="h-5 w-5" />
  </div>
</SparklesButton>`
        },
        {
          title: 'Custom Colors',
          description: 'Using custom colors for the sparkles effect.',
          component: (
            <SparklesButton 
              onClick={() => alert('Custom Colors Button clicked!')} 
              title="Custom Colors" 
              colors={{ first: "#22c55e", second: "#3b82f6" }}
            >
              <div className="p-2 rounded-full bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400">
                <Wand2 className="h-5 w-5" />
              </div>
            </SparklesButton>
          ),
          code: `<SparklesButton 
  onClick={handleClick} 
  title="Custom Colors" 
  colors={{ first: "#22c55e", second: "#3b82f6" }}
>
  <div className="p-2 rounded-full bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400">
    <Wand2 className="h-5 w-5" />
  </div>
</SparklesButton>`
        },
        {
          title: 'Disabled State',
          description: 'The button in a disabled state.',
          component: (
            <SparklesButton 
              onClick={() => alert('This should not trigger!')} 
              title="Disabled Button" 
              disabled={true}
            >
              <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                <Wand2 className="h-5 w-5" />
              </div>
            </SparklesButton>
          ),
          code: `<SparklesButton 
  onClick={handleClick} 
  title="Disabled Button" 
  disabled={true}
>
  <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
    <Wand2 className="h-5 w-5" />
  </div>
</SparklesButton>`
        },
        {
          title: 'More Sparkles',
          description: 'Increasing the number of sparkles for a more dramatic effect.',
          component: (
            <SparklesButton 
              onClick={() => alert('More Sparkles Button clicked!')} 
              title="More Sparkles" 
              sparklesCount={20}
            >
              <div className="p-2 rounded-full bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
                <Wand2 className="h-5 w-5" />
              </div>
            </SparklesButton>
          ),
          code: `<SparklesButton 
  onClick={handleClick} 
  title="More Sparkles" 
  sparklesCount={20}
>
  <div className="p-2 rounded-full bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
    <Wand2 className="h-5 w-5" />
  </div>
</SparklesButton>`
        }
      ]}
      bestPractices={{
        do: [
          'Use for AI-powered actions or features to provide visual emphasis.',
          'Keep the button content simple, preferably just an icon.',
          'Place in strategic locations where AI assistance is available.',
          'Use with the tooltip to explain the button\'s function.',
          'Apply appropriate colors that match your overall UI theme.'
        ],
        dont: [
          'Don\'t overuse throughout the interface, which would diminish its special nature.',
          'Don\'t use for primary actions where a standard button would be more appropriate.',
          'Don\'t remove the tooltip as it provides necessary context for the button\'s function.',
          'Don\'t place too close to important content as the sparkles might be distracting.',
          'Don\'t use colors that clash with your application\'s color scheme.'
        ]
      }}
      accessibility={[
        'Ensure the button has sufficient size (at least 44×44px) for easy touch target access.',
        'The title attribute provides tooltip context that helps explain the button\'s purpose.',
        'Maintain appropriate contrast ratios between the button and its background.',
        'The animation respects the prefers-reduced-motion user setting.',
        'Include proper focus styles for keyboard navigation.'
      ]}
    />
  );
};

export default SparklesButtonDoc;