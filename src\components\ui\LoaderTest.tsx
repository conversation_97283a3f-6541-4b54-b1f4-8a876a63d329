import React from 'react';
import { LoadingSpinner } from '../LoadingSpinner';
import { ResultidLoader } from './ResultidLoader';

const LoaderTest: React.FC = () => {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Loader Components Test</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">LoadingSpinner (Beam Animation)</h2>
        <div className="flex flex-wrap gap-8 items-center">
          <div className="flex flex-col items-center">
            <LoadingSpinner size="sm" />
            <span className="mt-2 text-sm text-gray-500">Small</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner size="md" />
            <span className="mt-2 text-sm text-gray-500">Medium (Default)</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner size="lg" />
            <span className="mt-2 text-sm text-gray-500">Large</span>
          </div>
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">ResultidLoader</h2>
        <div className="flex flex-wrap gap-8 items-center">
          <div className="flex flex-col items-center">
            <ResultidLoader size="sm" />
            <span className="mt-2 text-sm text-gray-500">Small</span>
          </div>
          <div className="flex flex-col items-center">
            <ResultidLoader size="md" />
            <span className="mt-2 text-sm text-gray-500">Medium (Default)</span>
          </div>
          <div className="flex flex-col items-center">
            <ResultidLoader size="lg" />
            <span className="mt-2 text-sm text-gray-500">Large</span>
          </div>
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Custom Colors (ResultidLoader)</h2>
        <div className="flex flex-wrap gap-8 items-center">
          <div className="flex flex-col items-center">
            <ResultidLoader className="text-white bg-purple-500" />
            <span className="mt-2 text-sm text-gray-500">Purple</span>
          </div>
          <div className="flex flex-col items-center">
            <ResultidLoader className="text-white bg-green-500" />
            <span className="mt-2 text-sm text-gray-500">Green</span>
          </div>
          <div className="flex flex-col items-center">
            <ResultidLoader className="text-black bg-yellow-300" />
            <span className="mt-2 text-sm text-gray-500">Yellow</span>
          </div>
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Dark Mode Simulation</h2>
        <div className="flex flex-wrap gap-8 items-center p-6 bg-gray-900 rounded-lg">
          <div className="flex flex-col items-center">
            <LoadingSpinner size="md" />
            <span className="mt-2 text-sm text-gray-300">LoadingSpinner</span>
          </div>
        </div>
        <p className="mt-2 text-xs text-gray-500">Note: This simulates appearance only. For true dark mode testing, toggle dark mode at the system or browser level.</p>
      </div>
    </div>
  );
};

export default LoaderTest;