import React from 'react';
import ComponentDoc from '../ComponentDoc';
import { 
  ChevronRight, User, Settings, Bell, Search, Home, BarChart, 
  FileText, Calendar, Mail, Info, Check, AlertCircle, X, 
  ThumbsUp, ThumbsDown, Download, Upload, Save, Edit, Trash, 
  Plus, Minus, PlusCircle, MinusCircle, Filter, ArrowRight, 
  ArrowLeft, MoreHorizontal, Clock, Folder, Tag, Star, Heart, 
  Share, Copy, ExternalLink, Globe, Menu 
} from 'lucide-react';

const IconographyDoc: React.FC = () => {
  const iconsGroups = [
    {
      title: 'Navigation & Actions',
      icons: [
        { icon: ChevronRight, name: 'ChevronRight' },
        { icon: ArrowRight, name: '<PERSON>Right' },
        { icon: ArrowLeft, name: 'ArrowLeft' },
        { icon: Home, name: 'Home' },
        { icon: Menu, name: 'Menu' },
        { icon: Search, name: 'Search' },
        { icon: ExternalLink, name: 'ExternalLink' },
      ]
    },
    {
      title: 'User Interface',
      icons: [
        { icon: User, name: 'User' },
        { icon: Setting<PERSON>, name: '<PERSON><PERSON><PERSON>' },
        { icon: <PERSON>, name: '<PERSON>' },
        { icon: Info, name: 'Info' },
        { icon: MoreHorizontal, name: 'MoreHorizontal' },
        { icon: Check, name: 'Check' },
        { icon: X, name: 'X' },
      ]
    },
    {
      title: 'Content',
      icons: [
        { icon: FileText, name: 'FileText' },
        { icon: Folder, name: 'Folder' },
        { icon: Calendar, name: 'Calendar' },
        { icon: Mail, name: 'Mail' },
        { icon: BarChart, name: 'BarChart' },
        { icon: Tag, name: 'Tag' },
      ]
    },
    {
      title: 'Feedback & Status',
      icons: [
        { icon: AlertCircle, name: 'AlertCircle' },
        { icon: ThumbsUp, name: 'ThumbsUp' },
        { icon: ThumbsDown, name: 'ThumbsDown' },
        { icon: Star, name: 'Star' },
        { icon: Heart, name: 'Heart' },
        { icon: Clock, name: 'Clock' },
      ]
    },
    {
      title: 'Actions',
      icons: [
        { icon: Download, name: 'Download' },
        { icon: Upload, name: 'Upload' },
        { icon: Save, name: 'Save' },
        { icon: Edit, name: 'Edit' },
        { icon: Trash, name: 'Trash' },
        { icon: Copy, name: 'Copy' },
        { icon: Share, name: 'Share' },
      ]
    },
    {
      title: 'Controls',
      icons: [
        { icon: Plus, name: 'Plus' },
        { icon: Minus, name: 'Minus' },
        { icon: PlusCircle, name: 'PlusCircle' },
        { icon: MinusCircle, name: 'MinusCircle' },
        { icon: Filter, name: 'Filter' },
        { icon: Globe, name: 'Globe' },
      ]
    },
  ];

  return (
    <ComponentDoc
      title="Iconography"
      description="Resultid uses Lucide icons throughout the platform for consistency and visual quality."
      noImport={true}
      noBestPractices={false}
      customContent={
        <div className="space-y-8">
          <section id="overview">
            <h2 className="text-xl font-semibold mb-4">Overview</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Icons play a crucial role in the Resultid user interface, providing visual cues and enhancing usability. 
              We exclusively use <a 
                href="https://lucide.dev/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline"
              >
                Lucide icons
              </a>, which provide a consistent, high-quality icon system that's easy to implement and customize.
            </p>
          </section>

          <section id="import">
            <h2 className="text-xl font-semibold mb-4">Import</h2>
            <div className="mb-4 rounded-md bg-gray-900 p-4">
              <pre className="text-white text-sm"><code>{`import { IconName } from 'lucide-react';

// Example
import { ChevronRight, User, Settings } from 'lucide-react';`}</code></pre>
            </div>
          </section>

          <section id="usage">
            <h2 className="text-xl font-semibold mb-4">Usage</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Lucide icons can be used directly as React components. They accept standard props like <code>className</code>, <code>size</code>, <code>color</code>, 
              and <code>strokeWidth</code> for customization.
            </p>

            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Basic Usage</h3>
              <div className="mb-4 rounded-md bg-gray-900 p-4">
                <pre className="text-white text-sm"><code>{`import { ChevronRight } from 'lucide-react';

function MyComponent() {
  return (
    <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md">
      <span>Next</span>
      <ChevronRight className="ml-1 h-4 w-4" />
    </button>
  );
}`}</code></pre>
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Customizing Icons</h3>
              <div className="mb-4 rounded-md bg-gray-900 p-4">
                <pre className="text-white text-sm"><code>{`// Size
<User size={24} />

// Color
<Bell className="text-blue-600" />
<AlertCircle color="#ef4444" />

// Stroke Width
<Star strokeWidth={1.5} />

// Combined
<Heart className="text-red-500" size={16} strokeWidth={2} />`}</code></pre>
              </div>
            </div>
          </section>

          <section id="icon-library">
            <h2 className="text-xl font-semibold mb-4">Icon Library</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Below is a selection of commonly used icons in the Resultid platform. 
              For the complete icon set, visit the <a 
                href="https://lucide.dev/icons/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline"
              >
                Lucide icon library
              </a>.
            </p>

            {iconsGroups.map((group, index) => (
              <div key={index} className="mb-10">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">{group.title}</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {group.icons.map((iconItem, iconIndex) => {
                    const IconComponent = iconItem.icon;
                    return (
                      <div 
                        key={iconIndex} 
                        className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
                      >
                        <IconComponent className="text-gray-800 dark:text-gray-200 mb-3" size={24} />
                        <span className="text-xs text-gray-600 dark:text-gray-400 text-center">{iconItem.name}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </section>

          <section id="best-practices">
            <h2 className="text-xl font-semibold mb-4">Best Practices</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-900/30 rounded-lg p-4">
                <h4 className="font-medium text-green-800 dark:text-green-400 mb-2">Do</h4>
                <ul className="pl-5 list-disc space-y-2 text-gray-600 dark:text-gray-300">
                  <li>Use Lucide icons exclusively for consistency</li>
                  <li>Keep icons a reasonable size (16-24px for UI elements)</li>
                  <li>Use icons to reinforce meaning, not replace text</li>
                  <li>Maintain a consistent stroke width across icons</li>
                  <li>Use color to enhance, not define meaning</li>
                  <li>Pair icons with labels for clarity</li>
                </ul>
              </div>
              
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-900/30 rounded-lg p-4">
                <h4 className="font-medium text-red-800 dark:text-red-400 mb-2">Don't</h4>
                <ul className="pl-5 list-disc space-y-2 text-gray-600 dark:text-gray-300">
                  <li>Mix icon libraries or styles</li>
                  <li>Use icons that are too small to be visible</li>
                  <li>Use icons alone when meaning may be unclear</li>
                  <li>Use multiple icons when one will suffice</li>
                  <li>Add unnecessary animations to icons</li>
                  <li>Rely on color alone to convey meaning</li>
                </ul>
              </div>
            </div>
          </section>

          <section id="accessibility">
            <h2 className="text-xl font-semibold mb-4">Accessibility</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Icons enhance visual comprehension but can create accessibility challenges if not implemented properly.
            </p>

            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Icon + Text</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              When icons are used alongside text, the icon should typically be treated as decorative, and the text should provide the context.
            </p>

            <div className="mb-6 rounded-md bg-gray-900 p-4">
              <pre className="text-white text-sm"><code>{`<button className="flex items-center gap-1.5">
  <Save aria-hidden="true" className="h-4 w-4" />
  <span>Save changes</span>
</button>`}</code></pre>
            </div>

            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Icon Only</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              For icon-only elements, always include appropriate ARIA labels:
            </p>

            <div className="mb-6 rounded-md bg-gray-900 p-4">
              <pre className="text-white text-sm"><code>{`<button 
  aria-label="Close dialog" 
  className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
>
  <X className="h-5 w-5" />
</button>`}</code></pre>
            </div>
          </section>

          <section id="examples">
            <h2 className="text-xl font-semibold mb-4">Examples</h2>

            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Button with Icon</h3>
            <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800">
              <div className="flex gap-4">
                <button className="flex items-center gap-1.5 px-3 py-1.5 bg-blue-600 text-white rounded-md">
                  <Save className="h-4 w-4" />
                  <span>Save</span>
                </button>

                <button className="flex items-center gap-1.5 px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md">
                  <span>Next</span>
                  <ArrowRight className="h-4 w-4" />
                </button>
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Icon Button Group</h3>
            <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800">
              <div className="inline-flex rounded-md shadow-sm">
                <button className="relative inline-flex items-center justify-center px-2.5 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-l-md">
                  <Edit className="h-4 w-4" />
                  <span className="sr-only">Edit</span>
                </button>
                <button className="relative inline-flex items-center justify-center px-2.5 py-2 border-t border-b border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                  <Copy className="h-4 w-4" />
                  <span className="sr-only">Copy</span>
                </button>
                <button className="relative inline-flex items-center justify-center px-2.5 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-r-md">
                  <Trash className="h-4 w-4" />
                  <span className="sr-only">Delete</span>
                </button>
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Notification Badge</h3>
            <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800">
              <div className="relative inline-block">
                <Bell className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                <span className="absolute top-0 right-0 flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                </span>
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Status Indicators</h3>
            <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800">
              <div className="flex gap-6">
                <div className="flex items-center gap-1.5">
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-sm">Success</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <AlertCircle className="h-5 w-5 text-amber-500" />
                  <span className="text-sm">Warning</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <X className="h-5 w-5 text-red-500" />
                  <span className="text-sm">Error</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <Info className="h-5 w-5 text-blue-500" />
                  <span className="text-sm">Information</span>
                </div>
              </div>
            </div>
          </section>
        </div>
      }
    />
  );
};

export default IconographyDoc;