import React from 'react';
import { LoadingSpinner } from '../LoadingSpinner';

interface ResultidLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * ResultidLoader - Standardized loader component
 * 
 * This is a compatibility component that passes through to LoadingSpinner.
 * For new code, use LoadingSpinner directly.
 * 
 * @deprecated Use LoadingSpinner component instead
 */
export function ResultidLoader({ size = 'md', className }: ResultidLoaderProps) {
  return (
    <LoadingSpinner size={size} className={className} />
  );
}