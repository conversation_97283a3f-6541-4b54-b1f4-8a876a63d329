import React, { useContext, useState, useEffect, useCallback } from "react";
import { createContext, useMemo } from "react";
import {
  getMenuItems,
  getProfileItems,
  structureMenuItems,
} from "@contexts/Menu/utils";
import { moduleTreeService } from "@lib/services/moduleTree";
import OperationDetailContext from "@contexts/OperationDetail";
import OperationsContext from "@contexts/Operations";
import LoginContext from "@contexts/Login";
import NavMenuContext from "@contexts/NavMenu";
import {
  getAndParseFunctionalitiesList,
  getFunctionalitiesList,
} from "@contexts/Operations/utils";
import {
  DELETE_OPERATION_MODULE,
  DOWNLOAD_OPERATION_MODULE,
  ENABLED_OPERATION_FILTER_MODULE,
  HELP_SPA_MODULE,
  MODULE_NAVIGATION,
  MULTIPLE_AUTHORIZATION_OPERATION_MODULE,
} from "@contexts/Operations/constants";
import NavigationContext from "@contexts/Navigation";
import { VirtualAssistantContext } from "..";
import { OPERATION_DETAILS_MODULE } from "@contexts/OperationDetail/constants";

const MenuContext = createContext(null);
const InitialState = {
  alert: false,
  alertHeight: 0,
  data: [],
  isTreeLoading: true,
  reloadSpa: false,
  showSpa: false,
};

const SPAS_MODULE = "47";

export const MenuProvider = ({ children, setSharedItems }) => {
  const { isAdmin } = useContext(LoginContext);
  const { handleLogout } = useContext(NavMenuContext);
  const { setFunctionalitiesList, setDetailsChildren } = useContext(
    OperationDetailContext
  );
  const {
    setDeleteOperationsFunctionalities,
    setEnabledOperationsFilterFunctionalities,
    setDownloadOperationsFunctionalities,
    setMultipleAuthModuleInfo,
  } = useContext(OperationsContext);
  const { setModuleNavigationBlackList } = useContext(NavigationContext);
  const { setHelpSPAModule } = useContext(VirtualAssistantContext);

  const [alert, setAlert] = useState(InitialState.alert);
  const [alertHeight, setAlertHeight] = useState(InitialState.alertHeight);
  const [data, setData] = useState(InitialState.data);
  const [isTreeLoading, setIsTreeLoading] = useState(
    InitialState.isTreeLoading
  );
  const [showSpa, setShowSpa] = useState(InitialState.showSpa);
  const [reloadSpa, setReloadSpa] = useState(InitialState.reloadSpa);
  const [spaList, setSpaList] = useState([]);

  const getModules = useCallback(async () => {
    try {
      const { data } = await moduleTreeService({
        code: "0",
        inheritanceLevel: "2",
      });
      if (!data.modules?.[0]?.availability?.isAvailable) {
        handleLogout();
      }
      setData(data.modules);
    } catch (err) {
      // grabar interaction login
    } finally {
      setIsTreeLoading(false);
    }
  }, [handleLogout]);

  const items = data && data.length ? data[0].childrenModules : [];

  const menuItems = getMenuItems(items);
  const profileItems = getProfileItems(items);

  useEffect(() => {
    getModules();
    return () => {
      setAlert(InitialState.alert);
      setAlertHeight(InitialState.alertHeight);
      setData(InitialState.data);
      setIsTreeLoading(InitialState.isTreeLoading);
      setShowSpa(InitialState.showSpa);
    };
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (items && Array.isArray(items) && items.length) {
      items.forEach((module) => {
        switch (module.idModule) {
          case OPERATION_DETAILS_MODULE:
            setFunctionalitiesList(getAndParseFunctionalitiesList(module));
            setDetailsChildren(module.childrenModules);
            break;

          case DELETE_OPERATION_MODULE:
            setDeleteOperationsFunctionalities(getFunctionalitiesList(module));
            break;

          case DOWNLOAD_OPERATION_MODULE:
            setDownloadOperationsFunctionalities(
              getFunctionalitiesList(module)
            );
            break;

          case MULTIPLE_AUTHORIZATION_OPERATION_MODULE:
            setMultipleAuthModuleInfo({
              functionalitiesList: getFunctionalitiesList(module),
              isOutOfService: !!module?.isOutOfService,
              isAvailable: !!module?.availability?.isAvailable,
            });
            break;

          case ENABLED_OPERATION_FILTER_MODULE:
            setEnabledOperationsFilterFunctionalities(true);
            break;

          case MODULE_NAVIGATION:
            const blackList = module?.extensions.blacklist.value || "";
            setModuleNavigationBlackList(blackList);
            break;

          case SPAS_MODULE:
            const spas = getAndParseFunctionalitiesList(module);
            setSpaList(spas);
            break;

          case HELP_SPA_MODULE:
            setHelpSPAModule(module);
            break;

          default:
            break;
        }
      });
      setSharedItems(menuItems, isAdmin);
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items]);

  const value = useMemo(() => {
    try {
      return {
        isTreeLoading,
        navItems: structureMenuItems(menuItems, isAdmin),
        spasToEmbed: spaList,
        profileItems,
        alert,
        setAlert,
        alertHeight,
        setAlertHeight,
        items,
        reloadSpa,
        setReloadSpa,
        showSpa,
        setShowSpa,
      };
    } catch {
      return {
        isTreeLoading,
        data,
        navItems: null,
        profileItems: null,
        reloadSpa,
        showSpa,
        alertHeight,
        alert,
        items,
      };
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isTreeLoading,
    menuItems,
    profileItems,
    data,
    reloadSpa,
    showSpa,
    alertHeight,
    alert,
    items,
  ]);

  return <MenuContext.Provider value={value}>{children}</MenuContext.Provider>;
};

export default MenuContext;
