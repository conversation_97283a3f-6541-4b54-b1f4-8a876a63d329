/**
 * Enhanced AI Chat hook with agent capabilities
 * Extends the useAIChat hook with support for tool execution and agent functionality
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import * as aiChatService from '../services/aiChatService';
import { ChatHistoryLoadReason } from '../services/aiChatService';
import type { AIMessage, ContextItem, ChatHistoryItem } from '../components/ui/AIAssistant/types';
import agentController from '../services/agent';
import { ToolExecution } from '../services/agent/tools/baseTool';

// Re-use types from the original hook
const ACTIVE_CHAT_STORAGE_KEY = 'resultid_active_chat_id';
type StorageEventListener = (event: StorageEvent) => void;

interface FetchHistoryResponse {
  currentPageChats: ChatHistoryItem[];
  otherChats: ChatHistoryItem[];
}

interface ChatLoadResult {
  success: boolean;
  messages?: AIMessage[];
  contextItems?: ContextItem[];
}

interface ChatFunctionsRef {
  loadChat: (chatId: string) => Promise<ChatLoadResult>;
  fetchAllChatHistory: (
    overrideEntityType?: string,
    overrideContextType?: string,
    overrideEntityId?: string,
    loadReason?: ChatHistoryLoadReason
  ) => Promise<FetchHistoryResponse>;
  resetConversation: () => void;
}

interface UseAgentChatOptions {
  contextType: 'tracker' | 'insights' | 'campaign' | 'general';
  entityId?: string;
  contextData?: any;
  loadHistoryOnMount?: boolean;
  useAgentByDefault?: boolean; // Whether to use the agent by default
}

interface SaveConversationResult {
  success: boolean;
  chatId?: string;
}

/**
 * Hook for AI chat with agent-powered tools functionality
 */
export function useAgentChat({
  contextType,
  entityId,
  contextData,
  loadHistoryOnMount = false,
  useAgentByDefault = true
}: UseAgentChatOptions) {
  // Re-use same state from original hook
  const { user } = useAuth();
  const { socket, isConnected, joinChat, leaveChat, leaveChatRoom } = useSocket();
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFetchingHistory, setIsFetchingHistory] = useState(false);
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);
  const [isLoadingChatHistory, setIsLoadingChatHistory] = useState(false);
  
  // Agent-specific state
  const [activeTools, setActiveTools] = useState<Record<string, ToolExecution>>({});
  const [isAgentEnabled, setIsAgentEnabled] = useState<boolean>(useAgentByDefault);
  
  const [currentChatId, setCurrentChatId] = useState<string | null>(() => {
    const storedChatId = localStorage.getItem(ACTIVE_CHAT_STORAGE_KEY);
    return storedChatId;
  });

  const effectiveEntityId = entityId === null ? null : (entityId || `default-${contextType}-${Date.now()}`);

  const chatFunctionsRef = useRef<ChatFunctionsRef>({
    loadChat: async () => { return { success: false } },
    fetchAllChatHistory: async () => { return { currentPageChats: [], otherChats: [] } },
    resetConversation: () => {}
  });
  
  // Define categorizeChats first so it can be used by other functions
  const categorizeChats = useCallback((chats: ChatHistoryItem[], ctxType?: string, entityId?: string): FetchHistoryResponse => {
    const currentPageChats: ChatHistoryItem[] = [];
    let otherChats: ChatHistoryItem[] = [];
    
    if (entityId && ctxType) {
      chats.forEach(chat => {
        if (chat.entityType === ctxType && chat.entityId === entityId) {
          currentPageChats.push(chat);
        } else {
          otherChats.push(chat);
        }
      });
    } else {
      otherChats = chats;
    }
    
    return { currentPageChats, otherChats };
  }, []);
  
  // Helper function to categorize existing chatHistory
  const categorizeExistingChats = useCallback((chats: ChatHistoryItem[], overrideContextType?: string, overrideEntityId?: string): FetchHistoryResponse => {
    const finalContextType = overrideContextType || contextType;
    const finalEntityId = overrideEntityId !== undefined ? 
      overrideEntityId : 
      (effectiveEntityId === null ? undefined : effectiveEntityId);
      
    return categorizeChats(chats, finalContextType, finalEntityId);
  }, [categorizeChats, contextType, effectiveEntityId]);

  const getDefaultWelcomeMessage = useCallback(() => {
    switch (contextType) {
      case 'tracker':
        return 'Hello! I can help you analyze your performance data. What would you like to know about your metrics?';
      case 'insights':
        return 'Hello! I can help you analyze and understand the data you\'re viewing. What would you like to know?';
      case 'campaign':
        return 'Hello! I can help you optimize your campaign performance. What would you like to know?';
      case 'general':
      default:
        return 'Hello! I\'m your Resultid AI assistant. How can I help you today?';
    }
  }, [contextType]);

  const resetConversation = useCallback(() => {
    console.log("useAgentChat: Resetting conversation with default welcome message");
    
    // Create welcome message
    const welcomeMessage = {
      type: 'ai',
      content: getDefaultWelcomeMessage(),
      timestamp: new Date().toISOString()
    } as AIMessage;
    
    // Reset messages to just the welcome message
    setMessages([welcomeMessage]);
    
    // Clear any errors
    setError(null);
    
    // Clear current chat ID and local storage
    setCurrentChatId(null);
    localStorage.removeItem(ACTIVE_CHAT_STORAGE_KEY);
    
    // Clear active tools
    setActiveTools({});
    
    // Force a complete reset of state so the UI updates accordingly
    setTimeout(() => {
      // Clear all internal tracking state that might prevent a proper reset
      fetchState.current.lastResponse = null;
      fetchState.current.lastReason = null;
      fetchState.current.lastFetch = 0;
    }, 10);
    
    console.log("Conversation reset complete");
  }, [getDefaultWelcomeMessage]);

  const fetchState = useRef({
    isLoading: false,
    lastFetch: 0,
    lastReason: null as ChatHistoryLoadReason | null,
    lastResponse: null as FetchHistoryResponse | null
  });
  
  const loadChat = useCallback(async (chatId: string): Promise<ChatLoadResult> => {
    if (!user?.organization?.organizationUUID) {
      console.error('Cannot load chat: Missing organization UUID');
      return { success: false };
    }
    
    setIsLoading(true);
    try {
      // Try to find the chat in the history first
      const chat = chatHistory.find(c => c.id === chatId);
      
      if (chat) {
        // We need to get the full chat with messages from the server
        let response;
        try {
          // First try the direct API endpoint for the chat ID (if it exists)
          response = await fetch(`${process.env.REACT_APP_API_URL || ''}/llm/chat/${chatId}?organization_uuid=${user.organization.organizationUUID}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
            }
          });
          
          if (!response.ok) {
            throw new Error(`Failed to load chat directly: ${response.status}`);
          }
          
          const data = await response.json();
          if (data.messages && data.messages.length > 0) {
            setMessages(data.messages);
            // Update localStorage with the current chat ID
            localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, chatId);
            
            return {
              success: true,
              messages: data.messages,
              contextItems: data.contextItems || []
            };
          }
        } catch {
          /* Silently continue with fallback */
        }
        
        response = await aiChatService.getChatHistory(
          chat.entityType || contextType,
          chat.entityId || (effectiveEntityId === null ? undefined : effectiveEntityId),
          user.organization.organizationUUID
        );
        
        if (response.messages && response.messages.length > 0) {
          setMessages(response.messages);
          // Update localStorage with the current chat ID
          localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, chatId);
          
          return {
            success: true,
            messages: response.messages,
            contextItems: response.contextItems || []
          };
        } else {
          const welcomeMessage: AIMessage = {
            type: 'ai',
            content: getDefaultWelcomeMessage(),
            timestamp: new Date().toISOString()
          };
          setMessages([welcomeMessage]);
          localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, chatId);
          return {
            success: true,
            messages: [welcomeMessage],
            contextItems: []
          };
        }
      } else {
        try {
          // First try to fetch all chat history to find this chat
          const allChats = await aiChatService.getAllChatHistory(
            user.organization.organizationUUID
          );
          
          // Find the requested chat
          const targetChat = allChats.chats.find(c => c.id === chatId);
          if (targetChat) {
            // If we have messages in the chat object, use them
            if (targetChat.messages && targetChat.messages.length > 0) {
              setMessages(targetChat.messages);
              // Update localStorage with the current chat ID
              localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, chatId);
              
              return {
                success: true,
                messages: targetChat.messages,
                contextItems: targetChat.contextItems || []
              };
            }
            
            // Otherwise try to load full history for this chat's entity
            const response = await aiChatService.getChatHistory(
              targetChat.entityType || contextType,
              targetChat.entityId || (effectiveEntityId === null ? undefined : effectiveEntityId),
              user.organization.organizationUUID
            );
            
            if (response.messages && response.messages.length > 0) {
              setMessages(response.messages);
              // Update localStorage with the current chat ID
              localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, chatId);
              
              return {
                success: true,
                messages: response.messages,
                contextItems: response.contextItems || []
              };
            }
          }
        } catch (err) {
          console.error('Error trying to find chat:', err);
        }
        
        const welcomeMessage: AIMessage = {
          type: 'ai',
          content: getDefaultWelcomeMessage(),
          timestamp: new Date().toISOString()
        };
        setMessages([welcomeMessage]);
        localStorage.removeItem(ACTIVE_CHAT_STORAGE_KEY);
        return {
          success: true,
          messages: [welcomeMessage],
          contextItems: []
        };
      }
    } catch (error) {
      console.error('Error loading chat:', error);
      const welcomeMessage: AIMessage = {
        type: 'ai',
        content: getDefaultWelcomeMessage(),
        timestamp: new Date().toISOString()
      };
      setMessages([welcomeMessage]);
      localStorage.removeItem(ACTIVE_CHAT_STORAGE_KEY);
      return {
        success: true,
        messages: [welcomeMessage],
        contextItems: []
      };
    } finally {
      setIsLoading(false);
    }
  }, [user?.organization?.organizationUUID, chatHistory, contextType, effectiveEntityId, getDefaultWelcomeMessage]);

  const fetchAllChatHistory = useCallback(async (
    overrideEntityType?: string,
    overrideContextType?: string, 
    overrideEntityId?: string,
    loadReason?: ChatHistoryLoadReason
  ) => {
    const now = Date.now();
    
    if (!loadReason || !Object.values(ChatHistoryLoadReason).includes(loadReason)) {
      console.warn('🛑 PREVENTING API CALL: No valid reason provided for fetchAllChatHistory');
      console.trace('Stack trace for invalid call:');
      
      if (chatHistory.length > 0) {
        return categorizeExistingChats(chatHistory, overrideContextType, overrideEntityId);
      }
      return { currentPageChats: [], otherChats: [] };
    }
    
    if (!user?.organization?.organizationUUID) {
      console.error('Cannot fetch chat history: Missing organization UUID');
      return { currentPageChats: [], otherChats: [] };
    }
    
    const finalContextType = overrideContextType || contextType;
    const finalEntityId = overrideEntityId !== undefined ? 
      overrideEntityId : 
      (effectiveEntityId === null ? undefined : effectiveEntityId);
    
    if (fetchState.current.isLoading) {
      console.warn('🛑 PREVENTING API CALL: Another fetch is already in progress');
      
      if (chatHistory.length > 0) {
        return categorizeExistingChats(chatHistory, finalContextType, finalEntityId);
      }
      
      if (fetchState.current.lastResponse) {
        return fetchState.current.lastResponse;
      }
      
      return { currentPageChats: [], otherChats: [] };
    }
    
    const DEBOUNCE_INTERVAL = loadReason === ChatHistoryLoadReason.MANUAL_REFRESH ? 1000 : 5000;
    const timeSinceLastFetch = now - fetchState.current.lastFetch;
    
    if (fetchState.current.lastReason === loadReason && 
        timeSinceLastFetch < DEBOUNCE_INTERVAL) {
      console.warn(`🛑 PREVENTING API CALL: Same reason "${loadReason}" called again too quickly (${timeSinceLastFetch}ms < ${DEBOUNCE_INTERVAL}ms)`);
      
      if (chatHistory.length > 0) {
        return categorizeExistingChats(chatHistory, finalContextType, finalEntityId);
      }
      
      if (fetchState.current.lastResponse) {
        return fetchState.current.lastResponse;
      }
      
      return { currentPageChats: [], otherChats: [] };
    }
    
    if (chatHistory.length > 0 && loadReason !== ChatHistoryLoadReason.MANUAL_REFRESH) {
      return categorizeExistingChats(chatHistory, finalContextType, finalEntityId);
    }
    
    fetchState.current.isLoading = true;
    fetchState.current.lastReason = loadReason;
    fetchState.current.lastFetch = now;
    
    setIsLoadingChatHistory(true);
    
    try {
      const response = await aiChatService.getAllChatHistory(
        user.organization.organizationUUID,
        finalContextType,
        finalEntityId,
        loadReason
      );
      
      if (!response || !response.chats || !Array.isArray(response.chats)) {
        console.warn('Invalid or empty response from getAllChatHistory:', response);
        setChatHistory([]);
        return { currentPageChats: [], otherChats: [] };
      }
      
      const processedChats = response.chats.map(chat => ({
        ...chat,
        // Ensure these fields exist with defaults if missing
        id: chat.id || `temp-${Date.now()}`,
        name: chat.name || 'Untitled Chat',
        preview: chat.preview || '',
        createdAt: chat.createdAt || new Date().toISOString(),
        updatedAt: chat.updatedAt || new Date().toISOString(),
        messages: chat.messages || [],
        contextItems: chat.contextItems || [],
        entityType: chat.entityType,
        entityId: chat.entityId
      } as ChatHistoryItem));
      
      const result = categorizeChats(processedChats, finalContextType, finalEntityId);
      
      fetchState.current.lastResponse = result;
      
      if (processedChats.length > 0) {
        setChatHistory(processedChats);
      }
      
      return result;
    } catch (error) {
      console.error('Error fetching all chat history:', error);
      // Don't reset chatHistory if we already have data - this preserves UI state
      if (chatHistory.length === 0) {
        setChatHistory([]);
      }
      return { currentPageChats: [], otherChats: [] };
    } finally {
      // Reset loading states
      setIsLoadingChatHistory(false);
      fetchState.current.isLoading = false;
    }
  }, [user?.organization?.organizationUUID, effectiveEntityId, contextType, chatHistory, categorizeExistingChats, categorizeChats]);

  // Update our function refs
  useEffect(() => {
    chatFunctionsRef.current = {
      loadChat,
      fetchAllChatHistory,
      resetConversation
    };
  }, [loadChat, fetchAllChatHistory, resetConversation]);

  // Load chat on initialization - prioritize localStorage chatId if available
  useEffect(() => {
    if (loadHistoryOnMount && user?.organization?.organizationUUID) {
      const initChat = async () => {
        setIsFetchingHistory(true);
        try {
          // First check if we have a stored chat ID in localStorage
          const storedChatId = localStorage.getItem(ACTIVE_CHAT_STORAGE_KEY);
          
          if (storedChatId) {
            // Try to load this specific chat
            const loadResult = await loadChat(storedChatId);
            if (loadResult.success) {
              setCurrentChatId(storedChatId); // Make sure we update the currentChatId state
              setIsFetchingHistory(false);
              return; // Exit early if we loaded the chat
            }
          }
          
          // Fallback: Get the most recent chat for this entity
          // Only apply entity filtering when effectiveEntityId is not null
          const allHistory = await aiChatService.getAllChatHistory(
            user.organization.organizationUUID,
            contextType,
            effectiveEntityId === null ? undefined : effectiveEntityId,
            ChatHistoryLoadReason.LOAD_FALLBACK // Use the proper enum value
          );
          
          // Check if we have any chats for this user
          if (allHistory.chats && allHistory.chats.length > 0) {
            // Sort by updatedAt to get the most recent chat
            const sortedChats = [...allHistory.chats].sort(
              (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
            
            // Get the most recent chat and load its messages
            const mostRecentChat = sortedChats[0];
            
            // Save the current chat ID so we can reference it later
            setCurrentChatId(mostRecentChat.id);
            localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, mostRecentChat.id);
            
            if (mostRecentChat.messages && mostRecentChat.messages.length > 0) {
              // If the chat has messages, use them
              setMessages(mostRecentChat.messages);
              return; // Exit early, we've loaded messages
            } else {
              // Try to load the chat from its ID
              const loadResult = await loadChat(mostRecentChat.id);
              if (loadResult.success) {
                return; // Exit early if we loaded the chat
              }
            }
          }
          
          // If we get here, there were no valid chats or messages in history
          // Initialize with welcome message
          const welcomeMessage: AIMessage = {
            type: 'ai',
            content: getDefaultWelcomeMessage(),
            timestamp: new Date().toISOString()
          };
          setMessages([welcomeMessage]);
          
        } catch (err) {
          console.error('Error initializing chat:', err);
          setError('Failed to load chat history');
          
          // Add default welcome message on error
          const errorWelcomeMessage: AIMessage = {
            type: 'ai',
            content: getDefaultWelcomeMessage(),
            timestamp: new Date().toISOString()
          };
          setMessages([errorWelcomeMessage]);
        } finally {
          setIsFetchingHistory(false);
        }
      };
      
      initChat();
    } else if (messages.length === 0) {
      // Initialize with welcome message if no history is loaded
      const initialWelcomeMessage: AIMessage = {
        type: 'ai',
        content: getDefaultWelcomeMessage(),
        timestamp: new Date().toISOString()
      };
      setMessages([initialWelcomeMessage]);
    }
  }, [loadHistoryOnMount, user?.organization?.organizationUUID, contextType, effectiveEntityId, getDefaultWelcomeMessage, loadChat, messages.length]);
  
  // Send a message - with agent enhancement
  const sendMessage = useCallback(async (content: string, contextItems?: ContextItem[]) => {
    if (!content.trim()) return;
    
    // Create user message
    const userMessage: AIMessage = {
      type: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString()
    };
    
    // Add user message to state
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);
    
    try {
      // Clear active tools before starting new interaction
      setActiveTools({});
      
      // Check whether to use agent or regular chat
      if (isAgentEnabled) {
        // Use the agent controller to process the message
        const agentResponse = await agentController.processMessage(content, {
          messages: [...messages, userMessage],
          contextItems,
          contextType,
          entityId: effectiveEntityId === null ? undefined : effectiveEntityId,
          contextData,
          // User permissions would be added here in a real implementation
          userPermissions: ['url:read'] // Basic permission for now
        });
        
        // Update tool executions if any
        if (agentResponse.tools && agentResponse.tools.length > 0) {
          const toolExecutions = agentResponse.tools.reduce((acc, tool) => ({
            ...acc,
            [tool.toolId]: tool
          }), {});
          
          setActiveTools(toolExecutions);
        }
        
        // Add the agent's response to messages
        setMessages(prev => [...prev, agentResponse.message]);
        
        // Try to emit via socket as well if connected
        if (isConnected && socket && user?.organization?.organizationUUID) {
          const roomId = `${user.organization.organizationUUID}_${contextType}_${effectiveEntityId}`;
          socket.emit('receive_message', {
            message: agentResponse.message,
            chatId: currentChatId,
            userId: user.userUUID,
            roomId
          });
        }
        
        // Save the conversation with updated context
        if (effectiveEntityId && user?.organization?.organizationUUID) {
          try {
            await aiChatService.saveChatHistory(
              contextType,
              effectiveEntityId,
              user.organization.organizationUUID,
              [...messages, userMessage, agentResponse.message],
              contextItems
            );
          } catch (error) {
            console.error('Error saving chat history:', error);
          }
        }
      } else {
        // Use the regular chat API
        // Emit "typing" indicator via socket if connected
        if (isConnected && socket && user?.organization?.organizationUUID) {
          const roomId = `${user.organization.organizationUUID}_${contextType}_${effectiveEntityId}`;
          socket.emit('new_message', {
            chatId: currentChatId,
            message: userMessage,
            user: user.userUUID,
            roomId
          });
        }
        
        // Make API call - the response will come through Socket.IO
        const response = await aiChatService.sendChatMessage({
          messages: [...messages, userMessage],
          contextType,
          contextData,
          contextItems, // Pass context items to the API
          entityId: effectiveEntityId === null ? undefined : effectiveEntityId,
          organizationUUID: user?.organization?.organizationUUID,
          socketId: socket?.id // Include the socket ID to allow direct messaging
        });
        
        // Use the direct API response immediately to ensure user sees a response
        if (response && response.message) {
          // Check if we already have this message to avoid duplicates
          const isDuplicate = messages.some(
            m => m.timestamp === response.message.timestamp && m.content === response.message.content
          );
          
          if (!isDuplicate) {
            setMessages(prev => [...prev, response.message]);
          }
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      
      // First set loading to false to prevent "Thinking..." message from showing
      setIsLoading(false);
      
      // Then set the error message
      setError('Failed to send message. Please try again.');
      
      // Add error message from AI
      const errorMessage: AIMessage = {
        type: 'ai',
        content: 'I apologize, but I encountered an error processing your request. Please try again or contact support if the issue persists.',
        timestamp: new Date().toISOString()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      // Always set loading to false
      setIsLoading(false);
    }
  }, [
    messages, 
    contextType, 
    contextData, 
    effectiveEntityId, 
    user?.organization?.organizationUUID,
    user?.userUUID,
    isConnected,
    socket,
    currentChatId,
    isAgentEnabled
  ]);
  
  // Socket setup - using same socket setup as original hook
  useEffect(() => {
    if (isConnected && socket && user?.organization?.organizationUUID) {
      
      // Join the appropriate rooms based on current context
      joinChat(currentChatId || 'default', contextType, effectiveEntityId === null ? undefined : effectiveEntityId);

      // Entity-based room for debugging
      const entityRoomId = `${user.organization.organizationUUID}_${contextType}_${effectiveEntityId}`;
      
      // Message receive handler
      interface SocketMessageData {
        message: AIMessage;
        chatId?: string;
        userId?: string;
        roomId?: string;
        [key: string]: unknown;
      }
      
      const handleReceiveMessage = (data: SocketMessageData) => {
        // Escape hatch for undefined message data
        if (!data || !data.message) {
          console.warn('Received malformed message data via socket:', data);
          return;
        }
        
        // Skip processing this socket message if we're no longer loading
        // This likely means we've already processed the direct API response
        if (!isLoading) {
          return;
        }
        
        // Check if we already have this message to prevent duplicates
        const isDuplicate = messages.some(
          m => m.timestamp === data.message.timestamp && m.content === data.message.content
        );
        
        if (!isDuplicate) {
          setMessages(prev => [...prev, data.message]);
        }
        
        // Always set loading to false when we receive a message
        setIsLoading(false);
      };
      
      // History refresh handler with safety improvements
      interface SocketRefreshData {
        chatId?: string;
        reason?: string;
        [key: string]: unknown;
      }
      
      const handleRefreshChatHistory = (data: SocketRefreshData) => {
        
        // Skip refreshing if not for our chat
        if (data.chatId && data.chatId !== currentChatId) {
          return;
        }
        
        // Use our ref instead of the actual function to avoid dependency issues
        const localFetchAllChatHistory = chatFunctionsRef.current.fetchAllChatHistory;
        if (!localFetchAllChatHistory) {
          console.warn('Cannot refresh chat history: fetch function not available');
          return;
        }
        
        // Trigger the refresh with a proper reason
        localFetchAllChatHistory(
          undefined, 
          undefined, 
          undefined, 
          ChatHistoryLoadReason.REOPEN // Use REOPEN reason for socket refreshes
        ).catch(err => {
          console.error('Error in socket-triggered history refresh:', err);
        });
      };
      
      // Chat deletion handler with safety improvements
      const handleChatDeleteNotification = (chatId: string) => {
        if (!chatId) {
          console.warn('Received malformed chat deletion notification');
          return;
        }
        
        // If the deleted chat is our current chat, reset the conversation
        if (chatId === currentChatId) {
          chatFunctionsRef.current.resetConversation();
        }
        
        // Update local state to remove the deleted chat
        setChatHistory(prev => prev.filter(c => c.id !== chatId));
      };
      
      // Register the event listeners
      socket.on('receive_message', handleReceiveMessage);
      socket.on('refresh_chat_history', handleRefreshChatHistory);
      socket.on('chat_delete_notification', handleChatDeleteNotification);
      
      // Clean up listeners on unmount or when dependencies change
      return () => {
        socket.off('receive_message', handleReceiveMessage);
        socket.off('refresh_chat_history', handleRefreshChatHistory);
        socket.off('chat_delete_notification', handleChatDeleteNotification);
        
        // Leave the entity room
        leaveChat(entityRoomId);
        
        // Leave the chat ID room if we have one
        if (currentChatId) {
          leaveChatRoom(currentChatId);
        }
      };
    }
  }, [
    isConnected, 
    socket, 
    user?.organization?.organizationUUID, 
    currentChatId, 
    contextType, 
    effectiveEntityId,
    messages,
    isLoading,
    joinChat,
    leaveChat,
    leaveChatRoom
  ]);

  // Create a ref to track previous chatId
  const prevChatIdRef = useRef<string | null>(null);

  // Effect to sync currentChatId with localStorage and listen for changes from other tabs
  useEffect(() => {
    
    // When currentChatId changes, update localStorage
    if (currentChatId) {
      localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, currentChatId);
      
      // When chat ID changes, join the new room and leave the old one
      if (isConnected && user?.organization?.organizationUUID) {
        // Leave the previous chat room if it exists
        if (prevChatIdRef.current && prevChatIdRef.current !== currentChatId) {
          leaveChatRoom(prevChatIdRef.current);
        }
        
        // Join the new chat room
        joinChat(currentChatId, contextType, effectiveEntityId === null ? undefined : effectiveEntityId);
        
        // Update the ref for next change
        prevChatIdRef.current = currentChatId;
      }
    } else {
      // If no current chat ID, remove from localStorage
      localStorage.removeItem(ACTIVE_CHAT_STORAGE_KEY);
      
      // Leave the previous chat room if it exists
      if (prevChatIdRef.current && isConnected) {
        leaveChatRoom(prevChatIdRef.current);
        prevChatIdRef.current = null;
      }
    }
    
    // Listen for changes from other tabs
    const handleStorageChange: StorageEventListener = (event) => {
      if (event.key === ACTIVE_CHAT_STORAGE_KEY && event.newValue !== currentChatId) {
        if (event.newValue) {
          // Load the chat that was activated in another tab
          setCurrentChatId(event.newValue);
          
          // If we have a valid chatId, load its messages
          if (user?.organization?.organizationUUID) {
            loadChat(event.newValue);
          }
        }
      }
    };
    
    // Add storage event listener
    window.addEventListener('storage', handleStorageChange);
    
    // Clean up
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      
      // Leave chat room on unmount if still connected
      if (currentChatId && isConnected) {
        leaveChatRoom(currentChatId);
      }
    };
  }, [
    currentChatId, 
    user?.organization?.organizationUUID, 
    isConnected,
    joinChat,
    leaveChatRoom,
    contextType, 
    effectiveEntityId,
    loadChat
  ]);
  
  // Save the current conversation with a name
  const saveConversation = useCallback(async (name: string, chatId?: string): Promise<SaveConversationResult> => {
    if (!user?.organization?.organizationUUID) {
      console.error('Cannot save chat: Missing organization UUID');
      return { success: false };
    }
    
    try {
      // Pass the contextItems directly from the messages state
      // This ensures we're using the current context items associated with this chat
      const currentContextItems = contextData?.contextItems || [];
      
      const result = await aiChatService.saveChat(
        name,
        messages,
        currentContextItems,
        user.organization.organizationUUID,
        contextType,
        effectiveEntityId === null ? undefined : effectiveEntityId,
        chatId // Pass chatId for updating existing chats
      );
      
      // If saving was successful and we got a chat ID back, store it
      if (result.success && result.chat?.chat_id) {
        setCurrentChatId(result.chat.chat_id);
        localStorage.setItem(ACTIVE_CHAT_STORAGE_KEY, result.chat.chat_id);
      }
      
      return {
        success: result.success,
        chatId: result.chat?.chat_id
      };
    } catch (error) {
      console.error('Error saving conversation:', error);
      return { success: false };
    }
  }, [messages, contextType, effectiveEntityId, contextData, user?.organization?.organizationUUID]);

  // Toggle agent functionality
  const toggleAgent = useCallback(() => {
    setIsAgentEnabled(prev => !prev);
  }, []);

  return {
    messages,
    isLoading,
    error,
    isFetchingHistory,
    chatHistory,
    isLoadingChatHistory,
    currentChatId,
    activeTools,  // New: tool executions
    isAgentEnabled, // New: agent toggle state
    sendMessage,
    resetConversation,
    saveConversation,
    fetchAllChatHistory,
    loadChat,
    toggleAgent // New: function to toggle agent functionality
  };
}

export default useAgentChat;