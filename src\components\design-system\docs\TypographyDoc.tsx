import React from 'react';
import ComponentDoc from '../ComponentDoc';

const TypographyDoc: React.FC = () => {
  return (
    <ComponentDoc
      title="Typography"
      description="Typography guidelines for consistent, readable, and accessible content across the Resultid platform."
      noImport={true}
      noBestPractices={false}
      customContent={
        <div className="space-y-12">
          <section id="overview">
            <h2 className="text-xl font-semibold mb-4">Overview</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Typography is a fundamental element of our design system, establishing hierarchy, 
              improving readability, and creating a consistent experience across the Resultid platform. 
              Our typography system uses a carefully selected font family with defined styles 
              for different content types.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Hierarchy</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Our type system creates clear visual hierarchy, guiding users through content 
                  and helping them quickly scan and comprehend information.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Readability</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Text is optimized for reading comfort with appropriate sizing, 
                  line heights, and spacing to reduce eye strain and improve comprehension.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Flexibility</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Our typography system is responsive and adaptable across different 
                  devices, screen sizes, and contexts while maintaining consistency.
                </p>
              </div>
            </div>
          </section>

          <section id="font-family">
            <h2 className="text-xl font-semibold mb-4">Font Family</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Resultid uses Inter Tight as our primary font family. Inter Tight is a variable font designed for screens 
              with excellent readability at various sizes. It offers a slightly more condensed alternative to Inter 
              with the same clean, modern aesthetic. We use the font with a variety of weights to establish hierarchy and emphasis.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="font-normal mb-3">
                  <p style={{ fontFamily: 'Inter Tight, sans-serif', fontSize: '18px', lineHeight: '1.6' }}>
                    Inter Tight Regular (400)
                  </p>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  The primary weight for body text and general content.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="font-medium mb-3">
                  <p style={{ fontFamily: 'Inter Tight, sans-serif', fontSize: '18px', lineHeight: '1.6' }}>
                    Inter Tight Medium (500)
                  </p>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Used for emphasis, small headings, and interactive elements.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="font-semibold mb-3">
                  <p style={{ fontFamily: 'Inter Tight, sans-serif', fontSize: '18px', lineHeight: '1.6' }}>
                    Inter Tight Semibold (600)
                  </p>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  For section headings and strong emphasis.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="font-bold mb-3">
                  <p style={{ fontFamily: 'Inter Tight, sans-serif', fontSize: '18px', lineHeight: '1.6' }}>
                    Inter Tight Bold (700)
                  </p>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  For primary headings and key interaction points.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="font-light mb-3">
                  <p style={{ fontFamily: 'Inter Tight, sans-serif', fontSize: '18px', lineHeight: '1.6' }}>
                    Inter Tight Light (300)
                  </p>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  For large display text and less emphasized content.
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="font-mono mb-3">
                  <p style={{ fontSize: '18px', lineHeight: '1.6' }}>
                    Monospace
                  </p>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Used for code, technical content, and data values.
                </p>
              </div>
            </div>
            
            <div className="mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Font Stack</h3>
              <div className="mb-4 rounded-md bg-gray-900 p-4">
                <pre className="text-white text-sm"><code>{`/* Font family utility classes */
.font-sans {
  font-family: "Inter Tight", ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 
               "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 
               "Liberation Mono", "Courier New", monospace;
}`}</code></pre>
              </div>
            </div>
          </section>

          <section id="type-scale">
            <h2 className="text-xl font-semibold mb-4">Type Scale</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our type scale provides a range of predefined text sizes with appropriate 
              line heights. This systematic approach ensures consistency across the platform
              while maintaining readable proportions.
            </p>
            
            <div className="space-y-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Headings</h3>
                <div className="space-y-6">
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text 4XL (36px / 40px)</p>
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white">The quick brown fox jumps over the lazy dog</h1>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text 3XL (30px / 36px)</p>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">The quick brown fox jumps over the lazy dog</h1>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text 2XL (24px / 32px)</p>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">The quick brown fox jumps over the lazy dog</h1>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text XL (20px / 28px)</p>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">The quick brown fox jumps over the lazy dog</h2>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text LG (18px / 28px)</p>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">The quick brown fox jumps over the lazy dog</h3>
                  </div>
                </div>
              </div>

              <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Body Text</h3>
                <div className="space-y-6">
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text Base (16px / 24px)</p>
                    <p className="text-base text-gray-600 dark:text-gray-300">
                      The quick brown fox jumps over the lazy dog. This is our primary body text size,
                      providing optimal readability for content. Lorem ipsum dolor sit amet, consectetur 
                      adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text SM (14px / 20px)</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      The quick brown fox jumps over the lazy dog. This size is used for secondary content, 
                      captions, metadata, and UI elements where space is limited. Lorem ipsum dolor sit amet,
                      consectetur adipiscing elit.
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Text XS (12px / 16px)</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      The quick brown fox jumps over the lazy dog. Used sparingly for fine print, legal text, captions, and metadata.
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Special Text</h3>
                <div className="space-y-6">
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Lead Text (18px / 28px)</p>
                    <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                      The quick brown fox jumps over the lazy dog. This size is used for lead paragraphs
                      and introductory text to provide emphasis and draw the reader in.
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Code Text</p>
                    <p className="font-mono text-sm text-gray-800 bg-gray-100 dark:bg-gray-700 dark:text-gray-200 p-2 rounded">
                      const message = "The quick brown fox jumps over the lazy dog";
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Overline Text (12px / uppercase / tracking wide)</p>
                    <p className="text-xs uppercase tracking-wider font-semibold text-gray-500 dark:text-gray-400">
                      The quick brown fox jumps over the lazy dog
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section id="usage-guidelines">
            <h2 className="text-xl font-semibold mb-4">Usage Guidelines</h2>
            
            <div className="space-y-8">
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Hierarchy & Structure</h3>
                
                <div className="space-y-8">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Page & Section Titles</h4>
                    <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                      <div className="mb-4">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">Campaign Performance</h2>
                        <p className="text-base text-gray-600 dark:text-gray-300">Overview of key metrics and insights for your marketing campaigns.</p>
                      </div>
                      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">Performance Summary</h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1">Engagement Rate</h4>
                            <p className="text-2xl font-bold text-gray-900 dark:text-white">24.8%</p>
                          </div>
                          <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1">Conversion Rate</h4>
                            <p className="text-2xl font-bold text-gray-900 dark:text-white">3.2%</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Content Hierarchy</h4>
                    <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Customer Feedback Analysis</h3>
                      <p className="text-base text-gray-600 dark:text-gray-300 mb-4">
                        Overview of sentiment and key themes from customer feedback collected over the past quarter.
                      </p>
                      
                      <h4 className="text-base font-medium text-gray-900 dark:text-white mb-2">Key Findings</h4>
                      <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1 mb-4">
                        <li>Overall sentiment improved by 12% compared to previous quarter</li>
                        <li>Product usability mentioned positively in 68% of feedback</li>
                        <li>Customer support response time remains the top concern</li>
                      </ul>
                      
                      <div className="text-sm text-gray-500 dark:text-gray-400 italic">
                        Analysis based on 1,248 customer feedback submissions between Jan-Mar 2023.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Text Styles for Different Components</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Cards & UI Components</h4>
                    <div className="rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <div className="p-4">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">Theme Analysis</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                          Automated analysis of key topics and sentiment in your dataset.
                        </p>
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-500 dark:text-gray-400">Updated 2 hours ago</span>
                          <a href="#" className="text-xs font-medium text-blue-600 dark:text-blue-400">View Details</a>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Forms & Interactive Elements</h4>
                    <div className="space-y-4">
                      <div className="space-y-1">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300" htmlFor="email">
                          Email Address
                        </label>
                        <input 
                          type="email" 
                          id="email" 
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base" 
                          placeholder="<EMAIL>"
                        />
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          We'll never share your email with anyone else.
                        </p>
                      </div>
                      
                      <div className="space-y-1">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300" htmlFor="category">
                          Category
                        </label>
                        <select 
                          id="category" 
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base"
                        >
                          <option>Select a category</option>
                          <option>Analytics</option>
                          <option>Reporting</option>
                          <option>Automation</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section id="responsive-behavior">
            <h2 className="text-xl font-semibold mb-4">Responsive Behavior</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our typography scales appropriately across different screen sizes to maintain 
              readability and hierarchy. We use a fluid system that adjusts text sizes 
              based on viewport width.
            </p>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Responsive Scaling Examples</h3>
              
              <div className="space-y-6">
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">{`Mobile (< 640px)`}</h4>
                  <div className="space-y-4">
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Page Title</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">Campaign Performance</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Section Heading</p>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">Performance Summary</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Body Text</p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Overview of key metrics and insights for your marketing campaigns.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Tablet (640px - 1024px)</h4>
                  <div className="space-y-4">
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Page Title</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">Campaign Performance</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Section Heading</p>
                      <p className="text-xl font-semibold text-gray-900 dark:text-white">Performance Summary</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Body Text</p>
                      <p className="text-base text-gray-600 dark:text-gray-300">
                        Overview of key metrics and insights for your marketing campaigns.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">{`Desktop (> 1024px)`}</h4>
                  <div className="space-y-4">
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Page Title</p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-white">Campaign Performance</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Section Heading</p>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-white">Performance Summary</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Body Text</p>
                      <p className="text-base text-gray-600 dark:text-gray-300">
                        Overview of key metrics and insights for your marketing campaigns.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section id="implementation">
            <h2 className="text-xl font-semibold mb-4">Implementation with Tailwind CSS</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our typography system is implemented using Tailwind CSS utility classes. 
              This provides a consistent and easy-to-use approach for applying typography 
              styles throughout the application.
            </p>
            
            <div className="mb-6 rounded-md bg-gray-900 p-4">
              <pre className="text-white text-sm"><code>{`<!-- Typography Class Examples -->

<!-- Headings -->
<h1 className="text-3xl font-bold text-gray-900 dark:text-white">Page Title</h1>
<h2 className="text-2xl font-semibold text-gray-900 dark:text-white">Section Heading</h2>
<h3 className="text-xl font-medium text-gray-900 dark:text-white">Subsection Title</h3>
<h4 className="text-lg font-medium text-gray-900 dark:text-white">Component Title</h4>

<!-- Body Text -->
<p className="text-base text-gray-600 dark:text-gray-300">Primary body text</p>
<p className="text-sm text-gray-600 dark:text-gray-300">Secondary body text</p>
<p className="text-xs text-gray-500 dark:text-gray-400">Caption text</p>

<!-- Special Text -->
<p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">Lead paragraph text</p>
<p className="text-xs uppercase tracking-wider font-semibold text-gray-500 dark:text-gray-400">Overline text</p>
<p className="font-mono text-sm">Code text</p>

<!-- Links -->
<a href="#" className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">Text link</a>

<!-- Responsive Typography -->
<h1 className="text-xl md:text-2xl lg:text-3xl font-bold">Responsive heading</h1>`}</code></pre>
            </div>
          </section>

          <section id="accessibility">
            <h2 className="text-xl font-semibold mb-4">Accessibility</h2>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Typography Accessibility Guidelines</h3>
              
              <div className="space-y-6">
                <div className="pb-4 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Text Size & Readability</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Body text should be at least 16px (or 1rem) for optimal readability</li>
                    <li>Avoid using text smaller than 12px except for legal text or metadata</li>
                    <li>Maintain appropriate line height (1.5x font size for body text) to improve readability</li>
                    <li>Ensure proper letter spacing, especially for all-caps text</li>
                  </ul>
                </div>
                
                <div className="pb-4 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Color & Contrast</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Text should have a minimum contrast ratio of 4.5:1 against its background</li>
                    <li>Large text (18pt or 14pt bold) should have a minimum contrast ratio of 3:1</li>
                    <li>Avoid using color alone to convey meaning in text</li>
                    <li>Ensure text is readable in both light and dark modes</li>
                  </ul>
                </div>
                
                <div className="pb-4 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Text Structure</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Use semantic HTML elements (h1-h6, p, etc.) for proper document structure</li>
                    <li>Maintain a logical heading hierarchy that doesn't skip levels</li>
                    <li>Ensure sufficient spacing between paragraphs and sections</li>
                    <li>Limit line length to 70-80 characters for optimal readability</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Font Customization</h4>
                  <ul className="list-disc pl-5 text-gray-600 dark:text-gray-300 space-y-1">
                    <li>Allow text to be resized up to 200% without breaking layout</li>
                    <li>Avoid using text in images where possible</li>
                    <li>Ensure text can adapt to user font size preferences</li>
                    <li>Make sure the site is usable when users override font settings</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <section id="best-practices">
            <h2 className="text-xl font-semibold mb-4">Best Practices</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-900/30 rounded-lg p-4">
                <h4 className="font-medium text-green-800 dark:text-green-400 mb-2">Do</h4>
                <ul className="pl-5 list-disc space-y-2 text-gray-600 dark:text-gray-300">
                  <li>Maintain consistent typographic hierarchy across pages</li>
                  <li>Use appropriate text sizes for different screen sizes</li>
                  <li>Ensure sufficient contrast between text and background</li>
                  <li>Use semantic HTML elements for text (h1-h6, p, etc.)</li>
                  <li>Keep line lengths reasonable (70-80 characters per line)</li>
                  <li>Use the defined font weights to create hierarchy</li>
                </ul>
              </div>
              
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-900/30 rounded-lg p-4">
                <h4 className="font-medium text-red-800 dark:text-red-400 mb-2">Don't</h4>
                <ul className="pl-5 list-disc space-y-2 text-gray-600 dark:text-gray-300">
                  <li>Use too many different text sizes on a single page</li>
                  <li>Combine too many font weights in close proximity</li>
                  <li>Set text too small to be readable (below 12px)</li>
                  <li>Use low-contrast text that's difficult to read</li>
                  <li>Create dense blocks of text without proper line height</li>
                  <li>Skip heading levels in document hierarchy</li>
                </ul>
              </div>
            </div>
          </section>
        </div>
      }
    />
  );
};

export default TypographyDoc;