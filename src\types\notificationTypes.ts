/**
 * Notification Types
 *
 * Type definitions for the notification system
 */

// Basic notification types
export type NotificationType =
  | "success"
  | "error"
  | "info"
  | "warning"
  | "custom";

// Priority levels for notifications
export type NotificationPriority = "low" | "medium" | "high" | "urgent";

// Action buttons that can be added to notifications
export interface NotificationAction {
  label: string;
  onClick: () => void;
  variant?: "primary" | "secondary" | "link";
}

// Content types that can be displayed in notifications
export interface NotificationContent {
  text?: string;
  html?: string;
  component?: React.ReactNode;
  image?: string;
  data?: any;
}

// Main notification interface
export interface Notification {
  id: string;
  type: NotificationType;
  title?: string;
  content: NotificationContent;
  duration?: number | null; // null means it won't auto-dismiss
  dismissible?: boolean;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
  timestamp: number;
  read: boolean;
  priority: NotificationPriority;
  icon?: string | React.ReactNode;

  // Additional properties for notification management
  isServerNotification?: boolean; // Whether this notification came from the server
  deleted?: boolean; // Whether this notification has been deleted
  dismissed?: boolean; // Whether this notification has been dismissed (auto or manual)
  inHistory?: boolean; // Whether this notification is being displayed in the history view
}

// Optional categories for organizing notifications
export type NotificationCategory =
  | "system"
  | "user"
  | "data"
  | "security"
  | "integration";
